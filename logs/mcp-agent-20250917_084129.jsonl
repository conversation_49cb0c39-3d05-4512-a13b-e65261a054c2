{"level":"INFO","timestamp":"2025-09-17T08:41:29.098772","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T08:41:29.100228","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T08:41:29.100261","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:41:29.100287","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:41:29.100306","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:41:29.100329","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.100349","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"4b0d3f19-87d9-45a3-801a-778ceb92b1a3"}}}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.172909","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.173148","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.173377","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.173415","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.173490","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.173504","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.173806","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174015","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174093","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174108","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174123","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174135","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174149","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174158","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174168","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174177","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174186","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174195","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174213","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174230","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174241","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174348","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174359","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T08:41:29.174462","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.920239","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T08:41:37.920443","namespace":"mcp_agent.financial_websocket_demo_client_768d34de","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T08:41:37.920469","namespace":"mcp_agent.financial_websocket_demo_client_768d34de","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:41:37.920491","namespace":"mcp_agent.financial_websocket_demo_client_768d34de","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:41:37.920506","namespace":"mcp_agent.financial_websocket_demo_client_768d34de","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:41:37.920520","namespace":"mcp_agent.financial_websocket_demo_client_768d34de","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.920538","namespace":"mcp_agent.financial_websocket_demo_client_768d34de","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_768d34de","agent_name":"mcp_application_loop","session_id":"fc4a1f49-c76b-4c6c-a743-7b50b1e04484"}}}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.920572","namespace":"mcp_agent.financial_websocket_demo_client_768d34de","message":"Financial session initialized for user client_768d34de, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.924479","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_849ec2f8"}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.924506","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.924517","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_849ec2f8"}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.924532","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.931548","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.931580","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-17T08:41:37.931945","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_849ec2f8.json"}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.931972","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_849ec2f8"}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.931987","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_849ec2f8"}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.931996","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_849ec2f8"}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.932022","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_849ec2f8"}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.932032","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.932055","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.932069","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.932084","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T08:41:37.932479","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T08:41:56.385488","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T08:42:08.422255","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T08:42:19.917189","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.395116","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.395614","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_849ec2f8.json"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.395657","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_849ec2f8"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.395675","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 72.46s"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.395696","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.395717","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.395728","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.396185","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.404871","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.405279","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_849ec2f8.json"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.405311","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_849ec2f8"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.405326","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.405344","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.405356","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.405366","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.405719","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.409658","namespace":"mcp_agent.agents.agent","message":"Initializing agent alert_manager_websocketorchestration..."}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.411428","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Found server configuration=","data":{"data":{"name":"alert-notification","description":null,"transport":"sse","command":null,"args":[],"url":"http://localhost:6972/sse","headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.411989","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Up and running with a persistent connection!"}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.454917","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.460057","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":{},"logging":null,"prompts":{"listChanged":false},"resources":{"subscribe":"False","listChanged":"False"},"tools":{"listChanged":"False"},"completions":null},"serverInfo":{"name":"example-mcp-server","title":null,"version":"1.11.0"},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.460122","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.461266","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.469238","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"TestTool","title":null,"description":"Simple test tool.","inputSchema":{"properties":{"message":{"description":"Test message","title":"Message","type":"string"}},"required":["message"],"title":"TestToolArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"HttpNotification","title":null,"description":"Sends an HTTP alert to a specified endpoint.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the notification","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the notification message","title":"Content","type":"string"}},"required":["alert_message"],"title":"HttpNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"MqttNotification","title":null,"description":"Sends an MQTT alert to a specified topic.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the alert","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the alert message","title":"Content","type":"string"}},"required":["alert_message"],"title":"MqttNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"EmailNotification","title":null,"description":"Sends an email notification.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the email","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the email message","title":"Content","type":"string"}},"required":["alert_message"],"title":"EmailNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.470012","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"prompts/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.475431","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"prompts":[]}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.475880","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"resources/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.481318","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"resources":[]}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.481395","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"MCP Aggregator initialized for server 'alert-notification'","data":{"data":{"progress_action":"Initialized","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration","tool_count":4,"prompt_count":0,"resource_count":"0"}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.481854","namespace":"mcp_agent.agents.agent","message":"Agent alert_manager_websocketorchestration initialized."}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.482502","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.482907","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.492574","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.493822","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-17T08:42:50.493901","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.493923","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.09s"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.493942","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_849ec2f8 completed in 72.56s"}
{"level":"DEBUG","timestamp":"2025-09-17T08:42:50.494344","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_849ec2f8.json"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.494381","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_849ec2f8"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.494396","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_849ec2f8 (success: True, time: 72.57s)"}
{"level":"INFO","timestamp":"2025-09-17T08:42:50.496062","namespace":"mcp_agent.financial_websocket_demo_client_768d34de","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_768d34de","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.124247","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T08:44:23.124531","namespace":"mcp_agent.financial_websocket_demo_debugger","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T08:44:23.124559","namespace":"mcp_agent.financial_websocket_demo_debugger","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:44:23.124581","namespace":"mcp_agent.financial_websocket_demo_debugger","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:44:23.124597","namespace":"mcp_agent.financial_websocket_demo_debugger","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:44:23.124610","namespace":"mcp_agent.financial_websocket_demo_debugger","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.124628","namespace":"mcp_agent.financial_websocket_demo_debugger","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_debugger","agent_name":"mcp_application_loop","session_id":"6c89a3b8-62bb-4161-b35d-46ad95bdd34c"}}}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.124665","namespace":"mcp_agent.financial_websocket_demo_debugger","message":"Financial session initialized for user debugger, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.127931","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_f4907834"}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.127955","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.127973","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_f4907834"}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.127989","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.128778","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.128798","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-17T08:44:23.129446","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_f4907834.json"}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.129481","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_f4907834"}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.129497","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_f4907834"}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.129508","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_f4907834"}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.129537","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_f4907834"}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.129549","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.129575","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.129590","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.129601","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T08:44:23.129983","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.067163","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-17T08:44:41.067731","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_f4907834.json"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.067779","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_f4907834"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.067797","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 17.94s"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.067816","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.067832","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.067842","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.068276","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.079508","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-17T08:44:41.079911","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_f4907834.json"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.079942","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_f4907834"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.079956","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.079971","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.079984","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.079995","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.080349","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.083998","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:44:41.084643","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:44:41.096497","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.098053","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-17T08:44:41.098126","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.098147","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.02s"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.098166","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_f4907834 completed in 17.97s"}
{"level":"DEBUG","timestamp":"2025-09-17T08:44:41.098604","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_f4907834.json"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.098640","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_f4907834"}
{"level":"INFO","timestamp":"2025-09-17T08:44:41.098661","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_f4907834 (success: True, time: 17.97s)"}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.480468","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:02:53.480817","namespace":"mcp_agent.financial_websocket_demo_client_0fdbb59f","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:02:53.480890","namespace":"mcp_agent.financial_websocket_demo_client_0fdbb59f","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:02:53.480922","namespace":"mcp_agent.financial_websocket_demo_client_0fdbb59f","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:02:53.480957","namespace":"mcp_agent.financial_websocket_demo_client_0fdbb59f","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:02:53.480989","namespace":"mcp_agent.financial_websocket_demo_client_0fdbb59f","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.481008","namespace":"mcp_agent.financial_websocket_demo_client_0fdbb59f","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_0fdbb59f","agent_name":"mcp_application_loop","session_id":"42d43a96-030b-4540-8ce0-b709f7168362"}}}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.481061","namespace":"mcp_agent.financial_websocket_demo_client_0fdbb59f","message":"Financial session initialized for user client_0fdbb59f, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.485210","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_ee7f7b06"}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.485234","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.485244","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_ee7f7b06"}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.485304","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.486391","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.486418","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-17T09:02:53.486957","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_ee7f7b06.json"}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.486985","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_ee7f7b06"}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.487008","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_ee7f7b06"}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.487018","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_ee7f7b06"}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.487114","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_ee7f7b06"}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.487126","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.487167","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.487209","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.487220","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:02:53.487665","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.499649","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:12.500366","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_ee7f7b06.json"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.500402","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_ee7f7b06"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.500420","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 19.01s"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.500450","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.500471","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.500490","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.500941","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.513252","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:12.513668","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_ee7f7b06.json"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.513697","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_ee7f7b06"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.513714","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.513730","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.513744","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.513762","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.514137","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.517905","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:12.519641","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:12.532507","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.534091","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-17T09:03:12.534174","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.534195","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.02s"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.534213","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_ee7f7b06 completed in 19.05s"}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:12.534591","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_ee7f7b06.json"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.534619","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_ee7f7b06"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.534638","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_ee7f7b06 (success: True, time: 19.05s)"}
{"level":"INFO","timestamp":"2025-09-17T09:03:12.538700","namespace":"mcp_agent.financial_websocket_demo_client_0fdbb59f","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_0fdbb59f","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T09:03:18.194538","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:18.194776","namespace":"mcp_agent.financial_websocket_research_test_user","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:18.194808","namespace":"mcp_agent.financial_websocket_research_test_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:18.194827","namespace":"mcp_agent.financial_websocket_research_test_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:18.194842","namespace":"mcp_agent.financial_websocket_research_test_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:18.194855","namespace":"mcp_agent.financial_websocket_research_test_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:03:18.194877","namespace":"mcp_agent.financial_websocket_research_test_user","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_research_test_user","agent_name":"mcp_application_loop","session_id":"7c6ea553-2e9c-4791-a323-7a273dee04ed"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:18.228794","namespace":"mcp_agent.agents.agent","message":"Initializing agent search_finder..."}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:18.230943","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"g-search: Found server configuration=","data":{"data":{"name":"g-search","description":null,"transport":"stdio","command":"npx","args":["-y","g-search-mcp"],"url":null,"headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:18.231189","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"fetch: Found server configuration=","data":{"data":{"name":"fetch","description":null,"transport":"stdio","command":"uvx","args":["mcp-server-fetch"],"url":null,"headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"INFO","timestamp":"2025-09-17T09:03:18.231457","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"g-search: Up and running with a persistent connection!"}
{"level":"INFO","timestamp":"2025-09-17T09:03:18.232076","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"fetch: Up and running with a persistent connection!"}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:18.253465","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:18.254349","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.379601","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":{},"logging":null,"prompts":{"listChanged":false},"resources":null,"tools":{"listChanged":"False"},"completions":null},"serverInfo":{"name":"mcp-fetch","title":null,"version":"1.14.0"},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.379742","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.380924","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.384115","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"fetch","title":null,"description":"Fetches a URL from the internet and optionally extracts its contents as markdown.\n\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.","inputSchema":{"description":"Parameters for fetching a URL.","properties":{"url":{"description":"URL to fetch","format":"uri","minLength":1,"title":"Url","type":"string"},"max_length":{"default":5000,"description":"Maximum number of characters to return.","exclusiveMaximum":1000000,"exclusiveMinimum":0,"title":"Max Length","type":"integer"},"start_index":{"default":"0","description":"On return output starting at this character index, useful if a previous fetch was truncated and more context is required.","minimum":"0","title":"Start Index","type":"integer"},"raw":{"default":false,"description":"Get the actual HTML content of the requested page, without simplification.","title":"Raw","type":"boolean"}},"required":["url"],"title":"Fetch","type":"object"},"outputSchema":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.384604","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"prompts/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.386698","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"prompts":[{"name":"fetch","title":null,"description":"Fetch a URL and extract its contents as markdown","arguments":[{"name":"url","description":"URL to fetch","required":true}],"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.387029","namespace":"mcp_agent.mcp.mcp_aggregator.search_finder","message":"Server 'fetch' does not support resources"}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.387145","namespace":"mcp_agent.mcp.mcp_aggregator.search_finder","message":"MCP Aggregator initialized for server 'fetch'","data":{"data":{"progress_action":"Initialized","server_name":"fetch","agent_name":"search_finder","tool_count":1,"prompt_count":"1","resource_count":0}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.423280","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2024-11-05","capabilities":{"experimental":null,"logging":null,"prompts":null,"resources":null,"tools":{"listChanged":null},"completions":null},"serverInfo":{"name":"g-search-mcp","title":null,"version":"0.1.0"},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.423369","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.424355","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.425625","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"search","title":null,"description":"Search on Google for multiple keywords and return the results","inputSchema":{"type":"object","properties":{"queries":{"type":"array","items":{"type":"string"},"description":"Array of search queries to perform"},"limit":{"type":"number","description":"Maximum number of results to return per query (default: 10)"},"timeout":{"type":"number","description":"Page loading timeout in milliseconds (default: 60000)"},"noSaveState":{"type":"boolean","description":"Whether to avoid saving browser state (default: false)"},"locale":{"type":"string","description":"Locale setting for search results (default: en-US)"},"debug":{"type":"boolean","description":"Whether to enable debug mode (showing browser window), overrides the --debug command line flag if specified"}},"required":["queries"]},"outputSchema":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.426033","namespace":"mcp_agent.mcp.mcp_aggregator.search_finder","message":"Server 'g-search' does not support prompts"}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.426218","namespace":"mcp_agent.mcp.mcp_aggregator.search_finder","message":"Server 'g-search' does not support resources"}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.426267","namespace":"mcp_agent.mcp.mcp_aggregator.search_finder","message":"MCP Aggregator initialized for server 'g-search'","data":{"data":{"progress_action":"Initialized","server_name":"g-search","agent_name":"search_finder","tool_count":1,"prompt_count":0,"resource_count":"0"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.426827","namespace":"mcp_agent.agents.agent","message":"Agent search_finder initialized."}
{"level":"INFO","timestamp":"2025-09-17T09:03:19.427406","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.search_finder","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:03:19.460267","namespace":"mcp_agent.financial_websocket_research_test_user","message":"Financial session initialized for user test_user, type: research"}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.461894","namespace":"mcp_agent.agents.agent","message":"Human input callback not set"}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.462055","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.search_finder","message":"vLLM request arguments: {'model': 'Qwen/Qwen3-32B', 'messages': [{'role': 'system', 'content': 'Use Google Search to find information about OpenAI in the current month of May 2025:\\n    You are a world class research analyst.\\n    Execute these exact search queries:\\n    1. \"OpenAI stock price today\"\\n    2. \"OpenAI latest quarterly earnings\"\\n    3. \"OpenAI financial news\"\\n    4. \"OpenAI earnings expectations\"\\n    \\n    Extract the most relevant information about:\\n    - Current stock price and recent movement\\n    - Latest earnings report data\\n    - Any significant recent news with correct citations\\n    \\n    Be smart and concise. Keep responses short and focused on facts.'}, {'role': 'user', 'content': 'Please research the latest financial information for OpenAI'}], 'max_tokens': 4096, 'temperature': 0.7, 'stop': None, 'tools': [{'type': 'function', 'function': {'name': 'fetch_fetch', 'description': 'Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.', 'parameters': {'description': 'Parameters for fetching a URL.', 'properties': {'url': {'description': 'URL to fetch', 'format': 'uri', 'minLength': 1, 'title': 'Url', 'type': 'string'}, 'max_length': {'default': 5000, 'description': 'Maximum number of characters to return.', 'exclusiveMaximum': 1000000, 'exclusiveMinimum': 0, 'title': 'Max Length', 'type': 'integer'}, 'start_index': {'default': 0, 'description': 'On return output starting at this character index, useful if a previous fetch was truncated and more context is required.', 'minimum': 0, 'title': 'Start Index', 'type': 'integer'}, 'raw': {'default': False, 'description': 'Get the actual HTML content of the requested page, without simplification.', 'title': 'Raw', 'type': 'boolean'}}, 'required': ['url'], 'title': 'Fetch', 'type': 'object'}}}, {'type': 'function', 'function': {'name': 'g-search_search', 'description': 'Search on Google for multiple keywords and return the results', 'parameters': {'type': 'object', 'properties': {'queries': {'type': 'array', 'items': {'type': 'string'}, 'description': 'Array of search queries to perform'}, 'limit': {'type': 'number', 'description': 'Maximum number of results to return per query (default: 10)'}, 'timeout': {'type': 'number', 'description': 'Page loading timeout in milliseconds (default: 60000)'}, 'noSaveState': {'type': 'boolean', 'description': 'Whether to avoid saving browser state (default: false)'}, 'locale': {'type': 'string', 'description': 'Locale setting for search results (default: en-US)'}, 'debug': {'type': 'boolean', 'description': 'Whether to enable debug mode (showing browser window), overrides the --debug command line flag if specified'}}, 'required': ['queries']}}}], 'extra_body': {'chat_template_kwargs': {'enable_thinking': True}}}"}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:19.462085","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.search_finder","message":"Chat in progress","data":{"data":{"progress_action":"Chatting","model":"Qwen/Qwen3-32B","agent_name":"search_finder","chat_turn":1}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:31.035709","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Raw vLLM API response (asyncio mode):","data":{"data":{"id":"chatcmpl-42b300d12a0d49de848dae45b202357a","choices":[{"finish_reason":"tool_calls","index":0,"logprobs":null,"message":{"content":"\n\n","refusal":null,"role":"assistant","annotations":null,"audio":null,"function_call":null,"tool_calls":[{"id":"chatcmpl-tool-5957384f6d8047cb8f2edd7b1b20382a","function":{"arguments":"{\"queries\": [\"OpenAI stock price today\", \"OpenAI latest quarterly earnings\", \"OpenAI financial news\", \"OpenAI earnings expectations\"], \"limit\": 5, \"locale\": \"en-US\"}","name":"g-search_search"},"type":"function"}],"reasoning_content":"\nOkay, the user wants the latest financial info on OpenAI. Let me start by figuring out what tools I have. There's the g-search_search function for Google searches and fetch_fetch to get web content. The user specified four search queries: stock price, quarterly earnings, financial news, and earnings expectations.\n\nFirst, I need to perform these searches. Since OpenAI isn't a publicly traded company, they don't have a stock price. But maybe there's speculation or private funding news. I'll use the g-search_search function with the given queries. \n\nFor each query, I'll need to run the search and extract relevant info. The first query is \"OpenAI stock price today.\" If OpenAI isn't public, the search might show that. The second is quarterly earnings, but private companies don't release quarterly earnings like public ones. However, maybe there are reports from their latest funding rounds. The third is financial news, which could include their $10 billion investment from Microsoft. The fourth is earnings expectations, which might be projections or analyst opinions.\n\nI should execute the Google searches first to gather the latest news and data. Then, if needed, use the fetch function to get specific articles. But since the user wants concise info, maybe the search results will suffice. Let me structure the tool calls accordingly. Each search query will be an array in the g-search_search function. Then, process the results to extract stock price info (if any), earnings data, significant news with citations, and expectations. \n\nWait, OpenAI is a private company, so they don't have a stock price. The search might return articles discussing potential IPOs or private valuations. I should note that in the response. For earnings, since they're private, maybe there's no public quarterly report, but there might be funding announcements. The financial news could include their latest funding round or partnerships. Earnings expectations might be speculative, so I need to check if there are any analyst reports or official statements.\n\nSo the steps are: perform the four Google searches, extract the most relevant and recent information from the results, and present the facts concisely. Make sure to cite sources properly. Since the user wants the current month of May 2025, I should check if the search results include recent articles from that period. Alright, let's proceed with the function calls.\n"},"stop_reason":null}],"created":1758070999,"model":"Qwen/Qwen3-32B","object":"chat.completion","service_tier":null,"system_fingerprint":null,"usage":{"completion_tokens":541,"prompt_tokens":757,"total_tokens":1298,"completion_tokens_details":null,"prompt_tokens_details":null},"prompt_logprobs":null,"kv_transfer_params":null}}}
{"level":"INFO","timestamp":"2025-09-17T09:03:31.035771","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Qwen Reasoning Content (choice 0):","data":{"data":{"reasoning":"\nOkay, the user wants the latest financial info on OpenAI. Let me start by figuring out what tools I have. There's the g-search_search function for Google searches and fetch_fetch to get web content. The user specified four search queries: stock price, quarterly earnings, financial news, and earnings expectations.\n\nFirst, I need to perform these searches. Since OpenAI isn't a publicly traded company, they don't have a stock price. But maybe there's speculation or private funding news. I'll use the g-search_search function with the given queries. \n\nFor each query, I'll need to run the search and extract relevant info. The first query is \"OpenAI stock price today.\" If OpenAI isn't public, the search might show that. The second is quarterly earnings, but private companies don't release quarterly earnings like public ones. However, maybe there are reports from their latest funding rounds. The third is financial news, which could include their $10 billion investment from Microsoft. The fourth is earnings expectations, which might be projections or analyst opinions.\n\nI should execute the Google searches first to gather the latest news and data. Then, if needed, use the fetch function to get specific articles. But since the user wants concise info, maybe the search results will suffice. Let me structure the tool calls accordingly. Each search query will be an array in the g-search_search function. Then, process the results to extract stock price info (if any), earnings data, significant news with citations, and expectations. \n\nWait, OpenAI is a private company, so they don't have a stock price. The search might return articles discussing potential IPOs or private valuations. I should note that in the response. For earnings, since they're private, maybe there's no public quarterly report, but there might be funding announcements. The financial news could include their latest funding round or partnerships. Earnings expectations might be speculative, so I need to check if there are any analyst reports or official statements.\n\nSo the steps are: perform the four Google searches, extract the most relevant and recent information from the results, and present the facts concisely. Make sure to cite sources properly. Since the user wants the current month of May 2025, I should check if the search results include recent articles from that period. Alright, let's proceed with the function calls.\n"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:31.040997","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.search_finder","message":"vLLM ChatCompletion response:","data":{"data":{"id":"chatcmpl-42b300d12a0d49de848dae45b202357a","choices":[{"finish_reason":"tool_calls","index":0,"logprobs":null,"message":{"content":"\n\n","refusal":null,"role":"assistant","annotations":null,"audio":null,"function_call":null,"tool_calls":[{"id":"chatcmpl-tool-5957384f6d8047cb8f2edd7b1b20382a","function":{"arguments":"{\"queries\": [\"OpenAI stock price today\", \"OpenAI latest quarterly earnings\", \"OpenAI financial news\", \"OpenAI earnings expectations\"], \"limit\": 5, \"locale\": \"en-US\"}","name":"g-search_search"},"type":"function"}]}}],"created":1758070999,"model":"Qwen/Qwen3-32B","object":"chat.completion","service_tier":null,"system_fingerprint":null,"usage":{"completion_tokens":541,"prompt_tokens":757,"total_tokens":1298,"completion_tokens_details":null,"prompt_tokens_details":null},"_request_id":null}}}
{"level":"INFO","timestamp":"2025-09-17T09:03:31.044523","namespace":"mcp_agent.mcp.mcp_aggregator.search_finder","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"search","server_name":"g-search","agent_name":"search_finder"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:03:31.045531","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"search","arguments":{"queries":["OpenAI stock price today","OpenAI latest quarterly earnings","OpenAI financial news","OpenAI earnings expectations"],"limit":5,"locale":"en-US"}}}}}
{"level":"INFO","timestamp":"2025-09-17T09:04:37.340892","namespace":"mcp_agent.financial_websocket_demo_debugger","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_debugger","agent_name":"mcp_application_loop"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:06:04.413064","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"{\n  \"searches\": [\n    {\n      \"query\": \"OpenAI stock price today\",\n      \"results\": [\n        {\n          \"title\": \"Search failed\",\n          \"link\": \"\",\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\nBrowser logs:\\n\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\n\u2551 Set either 'headless: true' or use 'xvfb-run <your-playwright-app>' before running Playwright. \u2551\\n\u2551                                                                                                \u2551\\n\u2551 <3 Playwright Team                                                                             \u2551\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\nCall log:\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-TiDOHM --remote-debugging-pipe --no-startup-window\\n  - <launched> pid=467619\\n  - [pid=467619][err] [467619:467631:0917/090603.997569:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\n  - [pid=467619][err] [467619:467619:0917/090603.998969:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\n  - [pid=467619][err] [467619:467619:0917/090603.998984:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\n\"\n        }\n      ]\n    },\n    {\n      \"query\": \"OpenAI latest quarterly earnings\",\n      \"results\": [\n        {\n          \"title\": \"Search failed\",\n          \"link\": \"\",\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\nBrowser logs:\\n\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\n\u2551 Set either 'headless: true' or use 'xvfb-run <your-playwright-app>' before running Playwright. \u2551\\n\u2551                                                                                                \u2551\\n\u2551 <3 Playwright Team                                                                             \u2551\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\nCall log:\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-dQXezM --remote-debugging-pipe --no-startup-window\\n  - <launched> pid=467639\\n  - [pid=467639][err] [467639:467653:0917/090604.173286:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\n  - [pid=467639][err] [467639:467639:0917/090604.174299:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\n  - [pid=467639][err] [467639:467639:0917/090604.174314:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\n\"\n        }\n      ]\n    },\n    {\n      \"query\": \"OpenAI financial news\",\n      \"results\": [\n        {\n          \"title\": \"Search failed\",\n          \"link\": \"\",\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\nBrowser logs:\\n\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\n\u2551 Set either 'headless: true' or use 'xvfb-run <your-playwright-app>' before running Playwright. \u2551\\n\u2551                                                                                                \u2551\\n\u2551 <3 Playwright Team                                                                             \u2551\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\nCall log:\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-YtDkuO --remote-debugging-pipe --no-startup-window\\n  - <launched> pid=467600\\n  - [pid=467600][err] [467600:467613:0917/090603.762790:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\n  - [pid=467600][err] [467600:467600:0917/090603.763717:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\n  - [pid=467600][err] [467600:467600:0917/090603.763733:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\n\"\n        }\n      ]\n    },\n    {\n      \"query\": \"OpenAI earnings expectations\",\n      \"results\": [\n        {\n          \"title\": \"Search failed\",\n          \"link\": \"\",\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\nBrowser logs:\\n\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\n\u2551 Set either 'headless: true' or use 'xvfb-run <your-playwright-app>' before running Playwright. \u2551\\n\u2551                                                                                                \u2551\\n\u2551 <3 Playwright Team                                                                             \u2551\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\nCall log:\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-1e1lcM --remote-debugging-pipe --no-startup-window\\n  - <launched> pid=467662\\n  - [pid=467662][err] [467662:467675:0917/090604.377554:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\n  - [pid=467662][err] [467662:467662:0917/090604.378483:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\n  - [pid=467662][err] [467662:467662:0917/090604.378498:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\n\"\n        }\n      ]\n    }\n  ]\n}","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:06:04.414974","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.search_finder","message":"Iteration 0: Tool call results: [{'role': 'tool', 'tool_call_id': 'chatcmpl-tool-5957384f6d8047cb8f2edd7b1b20382a', 'content': [{'type': 'text', 'text': '{\\n  \"searches\": [\\n    {\\n      \"query\": \"OpenAI stock price today\",\\n      \"results\": [\\n        {\\n          \"title\": \"Search failed\",\\n          \"link\": \"\",\\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\\\nBrowser logs:\\\\n\\\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\\\n\u2551 Set either \\'headless: true\\' or use \\'xvfb-run <your-playwright-app>\\' before running Playwright. \u2551\\\\n\u2551                                                                                                \u2551\\\\n\u2551 <3 Playwright Team                                                                             \u2551\\\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\\\nCall log:\\\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-TiDOHM --remote-debugging-pipe --no-startup-window\\\\n  - <launched> pid=467619\\\\n  - [pid=467619][err] [467619:467631:0917/090603.997569:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\\\n  - [pid=467619][err] [467619:467619:0917/090603.998969:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\\\n  - [pid=467619][err] [467619:467619:0917/090603.998984:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\\\n\"\\n        }\\n      ]\\n    },\\n    {\\n      \"query\": \"OpenAI latest quarterly earnings\",\\n      \"results\": [\\n        {\\n          \"title\": \"Search failed\",\\n          \"link\": \"\",\\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\\\nBrowser logs:\\\\n\\\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\\\n\u2551 Set either \\'headless: true\\' or use \\'xvfb-run <your-playwright-app>\\' before running Playwright. \u2551\\\\n\u2551                                                                                                \u2551\\\\n\u2551 <3 Playwright Team                                                                             \u2551\\\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\\\nCall log:\\\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-dQXezM --remote-debugging-pipe --no-startup-window\\\\n  - <launched> pid=467639\\\\n  - [pid=467639][err] [467639:467653:0917/090604.173286:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\\\n  - [pid=467639][err] [467639:467639:0917/090604.174299:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\\\n  - [pid=467639][err] [467639:467639:0917/090604.174314:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\\\n\"\\n        }\\n      ]\\n    },\\n    {\\n      \"query\": \"OpenAI financial news\",\\n      \"results\": [\\n        {\\n          \"title\": \"Search failed\",\\n          \"link\": \"\",\\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\\\nBrowser logs:\\\\n\\\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\\\n\u2551 Set either \\'headless: true\\' or use \\'xvfb-run <your-playwright-app>\\' before running Playwright. \u2551\\\\n\u2551                                                                                                \u2551\\\\n\u2551 <3 Playwright Team                                                                             \u2551\\\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\\\nCall log:\\\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-YtDkuO --remote-debugging-pipe --no-startup-window\\\\n  - <launched> pid=467600\\\\n  - [pid=467600][err] [467600:467613:0917/090603.762790:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\\\n  - [pid=467600][err] [467600:467600:0917/090603.763717:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\\\n  - [pid=467600][err] [467600:467600:0917/090603.763733:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\\\n\"\\n        }\\n      ]\\n    },\\n    {\\n      \"query\": \"OpenAI earnings expectations\",\\n      \"results\": [\\n        {\\n          \"title\": \"Search failed\",\\n          \"link\": \"\",\\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\\\nBrowser logs:\\\\n\\\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\\\n\u2551 Set either \\'headless: true\\' or use \\'xvfb-run <your-playwright-app>\\' before running Playwright. \u2551\\\\n\u2551                                                                                                \u2551\\\\n\u2551 <3 Playwright Team                                                                             \u2551\\\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\\\nCall log:\\\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-1e1lcM --remote-debugging-pipe --no-startup-window\\\\n  - <launched> pid=467662\\\\n  - [pid=467662][err] [467662:467675:0917/090604.377554:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\\\n  - [pid=467662][err] [467662:467662:0917/090604.378483:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\\\n  - [pid=467662][err] [467662:467662:0917/090604.378498:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\\\n\"\\n        }\\n      ]\\n    }\\n  ]\\n}'}]}]"}
{"level":"DEBUG","timestamp":"2025-09-17T09:06:04.415325","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.search_finder","message":"vLLM request arguments: {'model': 'Qwen/Qwen3-32B', 'messages': [{'role': 'system', 'content': 'Use Google Search to find information about OpenAI in the current month of May 2025:\\n    You are a world class research analyst.\\n    Execute these exact search queries:\\n    1. \"OpenAI stock price today\"\\n    2. \"OpenAI latest quarterly earnings\"\\n    3. \"OpenAI financial news\"\\n    4. \"OpenAI earnings expectations\"\\n    \\n    Extract the most relevant information about:\\n    - Current stock price and recent movement\\n    - Latest earnings report data\\n    - Any significant recent news with correct citations\\n    \\n    Be smart and concise. Keep responses short and focused on facts.'}, {'role': 'user', 'content': 'Please research the latest financial information for OpenAI'}, {'role': 'assistant', 'audio': None, 'refusal': None, 'content': '\\n\\n', 'tool_calls': [ChatCompletionMessageToolCall(id='chatcmpl-tool-5957384f6d8047cb8f2edd7b1b20382a', function=Function(arguments='{\"queries\": [\"OpenAI stock price today\", \"OpenAI latest quarterly earnings\", \"OpenAI financial news\", \"OpenAI earnings expectations\"], \"limit\": 5, \"locale\": \"en-US\"}', name='g-search_search'), type='function')]}, {'role': 'tool', 'tool_call_id': 'chatcmpl-tool-5957384f6d8047cb8f2edd7b1b20382a', 'content': [{'type': 'text', 'text': '{\\n  \"searches\": [\\n    {\\n      \"query\": \"OpenAI stock price today\",\\n      \"results\": [\\n        {\\n          \"title\": \"Search failed\",\\n          \"link\": \"\",\\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\\\nBrowser logs:\\\\n\\\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\\\n\u2551 Set either \\'headless: true\\' or use \\'xvfb-run <your-playwright-app>\\' before running Playwright. \u2551\\\\n\u2551                                                                                                \u2551\\\\n\u2551 <3 Playwright Team                                                                             \u2551\\\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\\\nCall log:\\\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-TiDOHM --remote-debugging-pipe --no-startup-window\\\\n  - <launched> pid=467619\\\\n  - [pid=467619][err] [467619:467631:0917/090603.997569:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\\\n  - [pid=467619][err] [467619:467619:0917/090603.998969:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\\\n  - [pid=467619][err] [467619:467619:0917/090603.998984:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\\\n\"\\n        }\\n      ]\\n    },\\n    {\\n      \"query\": \"OpenAI latest quarterly earnings\",\\n      \"results\": [\\n        {\\n          \"title\": \"Search failed\",\\n          \"link\": \"\",\\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\\\nBrowser logs:\\\\n\\\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\\\n\u2551 Set either \\'headless: true\\' or use \\'xvfb-run <your-playwright-app>\\' before running Playwright. \u2551\\\\n\u2551                                                                                                \u2551\\\\n\u2551 <3 Playwright Team                                                                             \u2551\\\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\\\nCall log:\\\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-dQXezM --remote-debugging-pipe --no-startup-window\\\\n  - <launched> pid=467639\\\\n  - [pid=467639][err] [467639:467653:0917/090604.173286:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\\\n  - [pid=467639][err] [467639:467639:0917/090604.174299:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\\\n  - [pid=467639][err] [467639:467639:0917/090604.174314:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\\\n\"\\n        }\\n      ]\\n    },\\n    {\\n      \"query\": \"OpenAI financial news\",\\n      \"results\": [\\n        {\\n          \"title\": \"Search failed\",\\n          \"link\": \"\",\\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\\\nBrowser logs:\\\\n\\\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\\\n\u2551 Set either \\'headless: true\\' or use \\'xvfb-run <your-playwright-app>\\' before running Playwright. \u2551\\\\n\u2551                                                                                                \u2551\\\\n\u2551 <3 Playwright Team                                                                             \u2551\\\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\\\nCall log:\\\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-YtDkuO --remote-debugging-pipe --no-startup-window\\\\n  - <launched> pid=467600\\\\n  - [pid=467600][err] [467600:467613:0917/090603.762790:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\\\n  - [pid=467600][err] [467600:467600:0917/090603.763717:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\\\n  - [pid=467600][err] [467600:467600:0917/090603.763733:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\\\n\"\\n        }\\n      ]\\n    },\\n    {\\n      \"query\": \"OpenAI earnings expectations\",\\n      \"results\": [\\n        {\\n          \"title\": \"Search failed\",\\n          \"link\": \"\",\\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\\\nBrowser logs:\\\\n\\\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\\\n\u2551 Set either \\'headless: true\\' or use \\'xvfb-run <your-playwright-app>\\' before running Playwright. \u2551\\\\n\u2551                                                                                                \u2551\\\\n\u2551 <3 Playwright Team                                                                             \u2551\\\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\\\nCall log:\\\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-1e1lcM --remote-debugging-pipe --no-startup-window\\\\n  - <launched> pid=467662\\\\n  - [pid=467662][err] [467662:467675:0917/090604.377554:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\\\n  - [pid=467662][err] [467662:467662:0917/090604.378483:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\\\n  - [pid=467662][err] [467662:467662:0917/090604.378498:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\\\n\"\\n        }\\n      ]\\n    }\\n  ]\\n}'}]}], 'max_tokens': 4096, 'temperature': 0.7, 'stop': None, 'tools': [{'type': 'function', 'function': {'name': 'fetch_fetch', 'description': 'Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.', 'parameters': {'description': 'Parameters for fetching a URL.', 'properties': {'url': {'description': 'URL to fetch', 'format': 'uri', 'minLength': 1, 'title': 'Url', 'type': 'string'}, 'max_length': {'default': 5000, 'description': 'Maximum number of characters to return.', 'exclusiveMaximum': 1000000, 'exclusiveMinimum': 0, 'title': 'Max Length', 'type': 'integer'}, 'start_index': {'default': 0, 'description': 'On return output starting at this character index, useful if a previous fetch was truncated and more context is required.', 'minimum': 0, 'title': 'Start Index', 'type': 'integer'}, 'raw': {'default': False, 'description': 'Get the actual HTML content of the requested page, without simplification.', 'title': 'Raw', 'type': 'boolean'}}, 'required': ['url'], 'title': 'Fetch', 'type': 'object'}}}, {'type': 'function', 'function': {'name': 'g-search_search', 'description': 'Search on Google for multiple keywords and return the results', 'parameters': {'type': 'object', 'properties': {'queries': {'type': 'array', 'items': {'type': 'string'}, 'description': 'Array of search queries to perform'}, 'limit': {'type': 'number', 'description': 'Maximum number of results to return per query (default: 10)'}, 'timeout': {'type': 'number', 'description': 'Page loading timeout in milliseconds (default: 60000)'}, 'noSaveState': {'type': 'boolean', 'description': 'Whether to avoid saving browser state (default: false)'}, 'locale': {'type': 'string', 'description': 'Locale setting for search results (default: en-US)'}, 'debug': {'type': 'boolean', 'description': 'Whether to enable debug mode (showing browser window), overrides the --debug command line flag if specified'}}, 'required': ['queries']}}}], 'extra_body': {'chat_template_kwargs': {'enable_thinking': True}}}"}
{"level":"DEBUG","timestamp":"2025-09-17T09:06:04.415349","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.search_finder","message":"Chat in progress","data":{"data":{"progress_action":"Chatting","model":"Qwen/Qwen3-32B","agent_name":"search_finder","chat_turn":2}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:06:11.802963","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Raw vLLM API response (asyncio mode):","data":{"data":{"id":"chatcmpl-cef82e9bbe6c440cb60cb2b5c223fff4","choices":[{"finish_reason":"stop","index":0,"logprobs":null,"message":{"content":"\n\nI encountered technical difficulties retrieving real-time financial data for OpenAI. The search tool failed to launch a browser due to missing system dependencies (XServer/DISPLAY configuration errors). This is an infrastructure issue preventing access to external financial resources. For up-to-date stock prices, earnings reports, and news about OpenAI, I recommend checking financial platforms like Yahoo Finance, Bloomberg, or OpenAI's official investor relations page directly.","refusal":null,"role":"assistant","annotations":null,"audio":null,"function_call":null,"tool_calls":[],"reasoning_content":"\nOkay, let's see. The user asked for the latest financial information on OpenAI. I used the g-search_search tool to perform the required queries. But all the searches failed with the same error message. The error mentions a problem with launching a headed browser without an XServer, which suggests there's an issue with the environment where the tool is running.\n\nHmm, since all the queries returned \"Search failed\" and there's no actual data retrieved, I need to inform the user about this problem. The error logs indicate that the browser couldn't start because of missing X server or DISPLAY settings. That's a technical issue on the backend. The user probably doesn't need to know the exact error details, but they should be aware that the information couldn't be fetched and maybe the reason why.\n\nI should explain that the system encountered technical difficulties while trying to access real-time data. Also, mention that the tools are having trouble connecting to external resources. Maybe suggest that they check back later or use another source for the latest financial updates on OpenAI. Keep the response polite and helpful, ensuring the user knows it's a temporary issue and not a lack of information.\n"},"stop_reason":null}],"created":1758071164,"model":"Qwen/Qwen3-32B","object":"chat.completion","service_tier":null,"system_fingerprint":null,"usage":{"completion_tokens":323,"prompt_tokens":4690,"total_tokens":5013,"completion_tokens_details":null,"prompt_tokens_details":null},"prompt_logprobs":null,"kv_transfer_params":null}}}
{"level":"INFO","timestamp":"2025-09-17T09:06:11.803028","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Qwen Reasoning Content (choice 0):","data":{"data":{"reasoning":"\nOkay, let's see. The user asked for the latest financial information on OpenAI. I used the g-search_search tool to perform the required queries. But all the searches failed with the same error message. The error mentions a problem with launching a headed browser without an XServer, which suggests there's an issue with the environment where the tool is running.\n\nHmm, since all the queries returned \"Search failed\" and there's no actual data retrieved, I need to inform the user about this problem. The error logs indicate that the browser couldn't start because of missing X server or DISPLAY settings. That's a technical issue on the backend. The user probably doesn't need to know the exact error details, but they should be aware that the information couldn't be fetched and maybe the reason why.\n\nI should explain that the system encountered technical difficulties while trying to access real-time data. Also, mention that the tools are having trouble connecting to external resources. Maybe suggest that they check back later or use another source for the latest financial updates on OpenAI. Keep the response polite and helpful, ensuring the user knows it's a temporary issue and not a lack of information.\n"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:06:11.803232","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.search_finder","message":"vLLM ChatCompletion response:","data":{"data":{"id":"chatcmpl-cef82e9bbe6c440cb60cb2b5c223fff4","choices":[{"finish_reason":"stop","index":0,"logprobs":null,"message":{"content":"\n\nI encountered technical difficulties retrieving real-time financial data for OpenAI. The search tool failed to launch a browser due to missing system dependencies (XServer/DISPLAY configuration errors). This is an infrastructure issue preventing access to external financial resources. For up-to-date stock prices, earnings reports, and news about OpenAI, I recommend checking financial platforms like Yahoo Finance, Bloomberg, or OpenAI's official investor relations page directly.","refusal":null,"role":"assistant","annotations":null,"audio":null,"function_call":null,"tool_calls":[]}}],"created":1758071164,"model":"Qwen/Qwen3-32B","object":"chat.completion","service_tier":null,"system_fingerprint":null,"usage":{"completion_tokens":323,"prompt_tokens":4690,"total_tokens":5013,"completion_tokens_details":null,"prompt_tokens_details":null},"_request_id":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:06:11.803303","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.search_finder","message":"Iteration 1: Stopping because finish_reason is 'stop'"}
{"level":"DEBUG","timestamp":"2025-09-17T09:06:11.803386","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.search_finder","message":"Chat finished","data":{"data":{"progress_action":"Finished","model":"Qwen/Qwen3-32B","agent_name":"search_finder"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:06:12.227980","namespace":"mcp_agent.agents.agent","message":"Shutting down agent search_finder..."}
{"level":"DEBUG","timestamp":"2025-09-17T09:06:12.228285","namespace":"mcp_agent.mcp.mcp_aggregator.search_finder","message":"Decremented connection ref count to 0"}
{"level":"INFO","timestamp":"2025-09-17T09:06:12.228306","namespace":"mcp_agent.mcp.mcp_aggregator.search_finder","message":"Last aggregator closing, shutting down all persistent connections..."}
{"level":"INFO","timestamp":"2025-09-17T09:06:12.228360","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"Disconnecting all persistent server connections..."}
{"level":"INFO","timestamp":"2025-09-17T09:06:12.228720","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"g-search: Requesting shutdown..."}
{"level":"INFO","timestamp":"2025-09-17T09:06:12.228748","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"fetch: Requesting shutdown..."}
{"level":"INFO","timestamp":"2025-09-17T09:06:12.228773","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"All persistent server connections signaled to disconnect."}
{"level":"DEBUG","timestamp":"2025-09-17T09:06:12.228796","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"MCPConnectionManager: shutting down all server tasks..."}
{"level":"INFO","timestamp":"2025-09-17T09:06:12.228806","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"Disconnecting all persistent server connections..."}
{"level":"INFO","timestamp":"2025-09-17T09:06:12.729558","namespace":"mcp_agent.mcp.mcp_aggregator.search_finder","message":"Connection manager successfully closed and removed from context"}
{"level":"DEBUG","timestamp":"2025-09-17T09:06:12.729659","namespace":"mcp_agent.agents.agent","message":"Agent search_finder shutdown."}
{"level":"INFO","timestamp":"2025-09-17T09:06:12.729702","namespace":"mcp_agent.financial_websocket_research_test_user","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_research_test_user","agent_name":"mcp_application_loop"}}}
