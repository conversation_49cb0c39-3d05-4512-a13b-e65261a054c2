{"level":"INFO","timestamp":"2025-09-18T09:22:45.131581","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-18T09:22:45.133106","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-18T09:22:45.133140","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T09:22:45.133168","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T09:22:45.133187","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T09:22:45.133208","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.133228","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"5087f46d-9de5-4726-940e-5835a97eca29"}}}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.206000","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.206249","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.206490","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.206536","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.206629","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.206645","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.206974","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207177","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207253","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207269","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207288","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207303","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207318","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207329","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207340","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207351","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207364","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207373","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207389","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207497","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207509","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207525","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207539","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-09-18T09:22:45.207648","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
