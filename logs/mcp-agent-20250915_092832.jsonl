{"level":"INFO","timestamp":"2025-09-15T09:28:32.405703","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-15T09:28:32.407603","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-15T09:28:32.407633","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-15T09:28:32.407657","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-15T09:28:32.407676","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-15T09:28:32.407695","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.407712","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"233982f0-cdfc-45e8-9ddd-ab3e246fc814"}}}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.513250","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.514356","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.515207","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.515251","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.515329","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.515343","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.515693","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.515970","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516060","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516081","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516095","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516215","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516231","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516240","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516255","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516264","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516277","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516288","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516312","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516328","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516338","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516351","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516361","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-09-15T09:28:32.516478","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
