{"level":"INFO","timestamp":"2025-09-17T09:48:30.454063","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:30.455388","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:30.455419","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:30.455444","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:30.455464","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:30.455482","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.455502","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"0ccb7762-b9f8-4469-9359-a7d671019d8f"}}}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.527633","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.527872","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528096","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528136","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528211","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528225","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528516","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528720","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528804","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528820","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528835","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528848","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528864","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528873","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528884","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528893","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528903","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528911","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528929","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528946","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.528956","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.529064","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.529075","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T09:48:30.529189","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
