{"level":"INFO","timestamp":"2025-09-03T15:54:19.536806","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-03T15:54:19.538838","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-03T15:54:19.538870","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-03T15:54:19.538895","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-03T15:54:19.538915","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-03T15:54:19.538929","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.538951","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"1c445935-c95f-4c3c-bd34-33c40588e350"}}}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.611952","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.613205","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614050","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614095","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614179","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614192","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614503","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614706","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614791","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614807","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614817","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614834","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614851","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614859","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614872","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614880","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614892","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614901","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.614914","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.615037","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.615049","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.615063","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.615073","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-09-03T15:54:19.615179","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-03T15:54:21.508425","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-03T15:54:21.508612","namespace":"mcp_agent.financial_websocket_demo_client_58e33d1f","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-03T15:54:21.508635","namespace":"mcp_agent.financial_websocket_demo_client_58e33d1f","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-03T15:54:21.508655","namespace":"mcp_agent.financial_websocket_demo_client_58e33d1f","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-03T15:54:21.508672","namespace":"mcp_agent.financial_websocket_demo_client_58e33d1f","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-03T15:54:21.508686","namespace":"mcp_agent.financial_websocket_demo_client_58e33d1f","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-03T15:54:21.508702","namespace":"mcp_agent.financial_websocket_demo_client_58e33d1f","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_58e33d1f","agent_name":"mcp_application_loop","session_id":"8a516275-4e94-46fd-befe-84b3ed108069"}}}
{"level":"INFO","timestamp":"2025-09-03T15:54:21.508731","namespace":"mcp_agent.financial_websocket_demo_client_58e33d1f","message":"Financial session initialized for user client_58e33d1f, type: demo"}
{"level":"INFO","timestamp":"2025-09-03T15:54:21.512271","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_8f957ffa"}
{"level":"INFO","timestamp":"2025-09-03T15:54:21.512294","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-03T15:54:21.512310","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-03T15:54:21.518797","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-03T15:54:21.518823","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-03T15:54:21.519173","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_8f957ffa.json"}
{"level":"INFO","timestamp":"2025-09-03T15:54:21.519198","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_8f957ffa"}
{"level":"INFO","timestamp":"2025-09-03T15:54:21.519212","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_8f957ffa"}
{"level":"INFO","timestamp":"2025-09-03T15:54:21.519240","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_8f957ffa"}
{"level":"INFO","timestamp":"2025-09-03T15:54:21.519263","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-03T15:54:21.519276","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.890477","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_8f957ffa.json"}
{"level":"INFO","timestamp":"2025-09-03T15:55:19.890522","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_8f957ffa"}
{"level":"INFO","timestamp":"2025-09-03T15:55:19.890540","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 58.37s"}
{"level":"INFO","timestamp":"2025-09-03T15:55:19.890558","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-03T15:55:19.890576","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.908819","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_8f957ffa.json"}
{"level":"INFO","timestamp":"2025-09-03T15:55:19.908854","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_8f957ffa"}
{"level":"INFO","timestamp":"2025-09-03T15:55:19.908871","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.02s"}
{"level":"INFO","timestamp":"2025-09-03T15:55:19.908889","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-03T15:55:19.908901","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.912184","namespace":"mcp_agent.agents.agent","message":"Initializing agent alert_manager_websocketorchestration..."}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.914040","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Found server configuration=","data":{"data":{"name":"alert-notification","description":null,"transport":"sse","command":null,"args":[],"url":"http://localhost:6972/sse","headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"INFO","timestamp":"2025-09-03T15:55:19.914571","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Up and running with a persistent connection!"}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.954741","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0"}}}}}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.959120","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":{},"logging":null,"prompts":{"listChanged":false},"resources":{"subscribe":"False","listChanged":"False"},"tools":{"listChanged":"False"},"completions":null},"serverInfo":{"name":"example-mcp-server","title":null,"version":"1.11.0"},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.959190","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.960315","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.966826","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"TestTool","title":null,"description":"Simple test tool.","inputSchema":{"properties":{"message":{"description":"Test message","title":"Message","type":"string"}},"required":["message"],"title":"TestToolArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"HttpNotification","title":null,"description":"Sends an HTTP alert to a specified endpoint.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the notification","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the notification message","title":"Content","type":"string"}},"required":["alert_message"],"title":"HttpNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"MqttNotification","title":null,"description":"Sends an MQTT alert to a specified topic.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the alert","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the alert message","title":"Content","type":"string"}},"required":["alert_message"],"title":"MqttNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"EmailNotification","title":null,"description":"Sends an email notification.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the email","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the email message","title":"Content","type":"string"}},"required":["alert_message"],"title":"EmailNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.967577","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"prompts/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.971805","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"prompts":[]}}}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.972428","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"resources/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.976596","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"resources":[]}}}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.976671","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"MCP Aggregator initialized for server 'alert-notification'","data":{"data":{"progress_action":"Initialized","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration","tool_count":4,"prompt_count":0,"resource_count":"0"}}}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.977122","namespace":"mcp_agent.agents.agent","message":"Agent alert_manager_websocketorchestration initialized."}
{"level":"INFO","timestamp":"2025-09-03T15:55:19.977770","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.978164","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.987540","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"ERROR","timestamp":"2025-09-03T15:55:19.988119","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-03T15:55:19.988143","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.08s"}
{"level":"INFO","timestamp":"2025-09-03T15:55:19.988162","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_8f957ffa completed in 58.47s"}
{"level":"DEBUG","timestamp":"2025-09-03T15:55:19.988528","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_8f957ffa.json"}
{"level":"INFO","timestamp":"2025-09-03T15:55:19.988554","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_8f957ffa"}
{"level":"INFO","timestamp":"2025-09-03T15:55:19.988568","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_8f957ffa (success: True, time: 58.48s)"}
{"level":"INFO","timestamp":"2025-09-03T15:55:19.990246","namespace":"mcp_agent.financial_websocket_demo_client_58e33d1f","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_58e33d1f","agent_name":"mcp_application_loop"}}}
