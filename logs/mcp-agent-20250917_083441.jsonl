{"level":"INFO","timestamp":"2025-09-17T08:34:41.168527","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T08:34:41.169875","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T08:34:41.169906","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:34:41.169932","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:34:41.169951","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:34:41.169975","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.169993","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"0efb9454-848f-48dd-9b37-b4f0e58a9a07"}}}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.242405","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.242628","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.242856","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.242897","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.242978","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.242992","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243296","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243510","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243587","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243603","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243614","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243627","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243645","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243654","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243664","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243675","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243685","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243693","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243708","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243724","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243738","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243852","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243863","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T08:34:41.243966","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.588144","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T08:34:45.588315","namespace":"mcp_agent.financial_websocket_demo_client_bec8690b","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T08:34:45.588339","namespace":"mcp_agent.financial_websocket_demo_client_bec8690b","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:34:45.588361","namespace":"mcp_agent.financial_websocket_demo_client_bec8690b","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:34:45.588376","namespace":"mcp_agent.financial_websocket_demo_client_bec8690b","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:34:45.588389","namespace":"mcp_agent.financial_websocket_demo_client_bec8690b","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.588405","namespace":"mcp_agent.financial_websocket_demo_client_bec8690b","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_bec8690b","agent_name":"mcp_application_loop","session_id":"62aa6dcb-4ad6-4153-b080-b85aa7d27c7f"}}}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.588435","namespace":"mcp_agent.financial_websocket_demo_client_bec8690b","message":"Financial session initialized for user client_bec8690b, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.591497","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_794724a3"}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.591518","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.591676","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_794724a3"}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.591700","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.598169","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.598197","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-17T08:34:45.598554","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_794724a3.json"}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.598583","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_794724a3"}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.598597","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_794724a3"}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.598607","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_794724a3"}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.598634","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_794724a3"}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.598644","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.598666","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.598680","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.598691","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T08:34:45.599108","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T08:35:00.691316","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T08:35:14.526961","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T08:35:43.845738","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.653381","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.653863","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_794724a3.json"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.653900","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_794724a3"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.653917","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 89.06s"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.653937","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.653954","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.653964","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.654352","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.662576","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.662976","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_794724a3.json"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.663006","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_794724a3"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.663020","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.663037","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.663048","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.663058","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.663375","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.667214","namespace":"mcp_agent.agents.agent","message":"Initializing agent alert_manager_websocketorchestration..."}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.670052","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Found server configuration=","data":{"data":{"name":"alert-notification","description":null,"transport":"sse","command":null,"args":[],"url":"http://localhost:6972/sse","headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.671281","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.671453","namespace":"mcp_agent.financial_websocket_demo_client_698d1dcb","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.671476","namespace":"mcp_agent.financial_websocket_demo_client_698d1dcb","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.671493","namespace":"mcp_agent.financial_websocket_demo_client_698d1dcb","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.671513","namespace":"mcp_agent.financial_websocket_demo_client_698d1dcb","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.671526","namespace":"mcp_agent.financial_websocket_demo_client_698d1dcb","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.671540","namespace":"mcp_agent.financial_websocket_demo_client_698d1dcb","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_698d1dcb","agent_name":"mcp_application_loop","session_id":"7bc0a66a-c876-4474-b51a-14fa3c6bda86"}}}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.671573","namespace":"mcp_agent.financial_websocket_demo_client_698d1dcb","message":"Financial session initialized for user client_698d1dcb, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.672603","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Up and running with a persistent connection!"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.675805","namespace":"mcp_agent.financial_websocket_demo_client_698d1dcb","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_698d1dcb","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.676529","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.676660","namespace":"mcp_agent.financial_websocket_demo_client_99c49e24","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.676680","namespace":"mcp_agent.financial_websocket_demo_client_99c49e24","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.676697","namespace":"mcp_agent.financial_websocket_demo_client_99c49e24","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.676710","namespace":"mcp_agent.financial_websocket_demo_client_99c49e24","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.676723","namespace":"mcp_agent.financial_websocket_demo_client_99c49e24","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.676741","namespace":"mcp_agent.financial_websocket_demo_client_99c49e24","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_99c49e24","agent_name":"mcp_application_loop","session_id":"01c2eb37-8144-49d3-bc3d-caf479300316"}}}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.676769","namespace":"mcp_agent.financial_websocket_demo_client_99c49e24","message":"Financial session initialized for user client_99c49e24, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.711725","namespace":"mcp_agent.financial_websocket_demo_client_99c49e24","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_99c49e24","agent_name":"mcp_application_loop"}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.727285","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.736707","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":{},"logging":null,"prompts":{"listChanged":false},"resources":{"subscribe":"False","listChanged":"False"},"tools":{"listChanged":"False"},"completions":null},"serverInfo":{"name":"example-mcp-server","title":null,"version":"1.11.0"},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.736808","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.738191","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.750177","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"TestTool","title":null,"description":"Simple test tool.","inputSchema":{"properties":{"message":{"description":"Test message","title":"Message","type":"string"}},"required":["message"],"title":"TestToolArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"HttpNotification","title":null,"description":"Sends an HTTP alert to a specified endpoint.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the notification","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the notification message","title":"Content","type":"string"}},"required":["alert_message"],"title":"HttpNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"MqttNotification","title":null,"description":"Sends an MQTT alert to a specified topic.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the alert","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the alert message","title":"Content","type":"string"}},"required":["alert_message"],"title":"MqttNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"EmailNotification","title":null,"description":"Sends an email notification.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the email","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the email message","title":"Content","type":"string"}},"required":["alert_message"],"title":"EmailNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.751011","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"prompts/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.756770","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"prompts":[]}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.757236","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"resources/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.762826","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"resources":[]}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.762907","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"MCP Aggregator initialized for server 'alert-notification'","data":{"data":{"progress_action":"Initialized","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration","tool_count":4,"prompt_count":0,"resource_count":"0"}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.763389","namespace":"mcp_agent.agents.agent","message":"Agent alert_manager_websocketorchestration initialized."}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.764112","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.764524","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.776067","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.777320","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-17T08:36:14.777411","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.777434","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.11s"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.777457","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_794724a3 completed in 89.18s"}
{"level":"DEBUG","timestamp":"2025-09-17T08:36:14.777926","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_794724a3.json"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.777960","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_794724a3"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.777985","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_794724a3 (success: True, time: 89.19s)"}
{"level":"INFO","timestamp":"2025-09-17T08:36:14.779647","namespace":"mcp_agent.financial_websocket_demo_client_bec8690b","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_bec8690b","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.622287","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T08:37:21.622485","namespace":"mcp_agent.financial_websocket_demo_client_7ddf1fff","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T08:37:21.622508","namespace":"mcp_agent.financial_websocket_demo_client_7ddf1fff","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:37:21.622528","namespace":"mcp_agent.financial_websocket_demo_client_7ddf1fff","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:37:21.622544","namespace":"mcp_agent.financial_websocket_demo_client_7ddf1fff","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:37:21.622559","namespace":"mcp_agent.financial_websocket_demo_client_7ddf1fff","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.622576","namespace":"mcp_agent.financial_websocket_demo_client_7ddf1fff","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_7ddf1fff","agent_name":"mcp_application_loop","session_id":"955b466f-7fff-40c0-9117-388236cfc7e7"}}}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.622605","namespace":"mcp_agent.financial_websocket_demo_client_7ddf1fff","message":"Financial session initialized for user client_7ddf1fff, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.626062","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_c4c3a2e4"}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.626086","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.626097","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_c4c3a2e4"}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.626113","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.626884","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.626902","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-17T08:37:21.627233","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_c4c3a2e4.json"}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.627258","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_c4c3a2e4"}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.627272","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_c4c3a2e4"}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.627282","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_c4c3a2e4"}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.627314","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_c4c3a2e4"}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.627324","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.627348","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.627361","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.627371","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T08:37:21.627744","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.854255","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-17T08:37:39.854945","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_c4c3a2e4.json"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.854981","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_c4c3a2e4"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.854997","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 18.23s"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.855015","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.855028","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.855038","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.855466","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.862700","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-17T08:37:39.863053","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_c4c3a2e4.json"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.863081","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_c4c3a2e4"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.863095","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.863111","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.863124","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.863133","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.863471","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.866145","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:37:39.866737","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:37:39.879022","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.880484","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-17T08:37:39.880550","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.880570","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.02s"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.880587","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_c4c3a2e4 completed in 18.25s"}
{"level":"DEBUG","timestamp":"2025-09-17T08:37:39.880964","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_c4c3a2e4.json"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.880991","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_c4c3a2e4"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.881007","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_c4c3a2e4 (success: True, time: 18.25s)"}
{"level":"INFO","timestamp":"2025-09-17T08:37:39.884771","namespace":"mcp_agent.financial_websocket_demo_client_7ddf1fff","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_7ddf1fff","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T08:38:35.112047","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:35.112214","namespace":"mcp_agent.financial_websocket_research_test_user","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:35.112236","namespace":"mcp_agent.financial_websocket_research_test_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:35.112254","namespace":"mcp_agent.financial_websocket_research_test_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:35.112268","namespace":"mcp_agent.financial_websocket_research_test_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:35.112281","namespace":"mcp_agent.financial_websocket_research_test_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T08:38:35.112296","namespace":"mcp_agent.financial_websocket_research_test_user","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_research_test_user","agent_name":"mcp_application_loop","session_id":"38980b6d-e1e4-4593-bffb-e7857b663678"}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:35.144860","namespace":"mcp_agent.agents.agent","message":"Initializing agent search_finder..."}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:35.146696","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"g-search: Found server configuration=","data":{"data":{"name":"g-search","description":null,"transport":"stdio","command":"npx","args":["-y","g-search-mcp"],"url":null,"headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:35.146906","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"fetch: Found server configuration=","data":{"data":{"name":"fetch","description":null,"transport":"stdio","command":"uvx","args":["mcp-server-fetch"],"url":null,"headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"INFO","timestamp":"2025-09-17T08:38:35.147149","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"g-search: Up and running with a persistent connection!"}
{"level":"INFO","timestamp":"2025-09-17T08:38:35.147665","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"fetch: Up and running with a persistent connection!"}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:35.165787","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:35.167100","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.234347","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":{},"logging":null,"prompts":{"listChanged":false},"resources":null,"tools":{"listChanged":"False"},"completions":null},"serverInfo":{"name":"mcp-fetch","title":null,"version":"1.14.0"},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.234453","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.235497","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.239122","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"fetch","title":null,"description":"Fetches a URL from the internet and optionally extracts its contents as markdown.\n\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.","inputSchema":{"description":"Parameters for fetching a URL.","properties":{"url":{"description":"URL to fetch","format":"uri","minLength":1,"title":"Url","type":"string"},"max_length":{"default":5000,"description":"Maximum number of characters to return.","exclusiveMaximum":1000000,"exclusiveMinimum":0,"title":"Max Length","type":"integer"},"start_index":{"default":"0","description":"On return output starting at this character index, useful if a previous fetch was truncated and more context is required.","minimum":"0","title":"Start Index","type":"integer"},"raw":{"default":false,"description":"Get the actual HTML content of the requested page, without simplification.","title":"Raw","type":"boolean"}},"required":["url"],"title":"Fetch","type":"object"},"outputSchema":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.239614","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"prompts/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.241874","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"prompts":[{"name":"fetch","title":null,"description":"Fetch a URL and extract its contents as markdown","arguments":[{"name":"url","description":"URL to fetch","required":true}],"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.242195","namespace":"mcp_agent.mcp.mcp_aggregator.search_finder","message":"Server 'fetch' does not support resources"}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.242271","namespace":"mcp_agent.mcp.mcp_aggregator.search_finder","message":"MCP Aggregator initialized for server 'fetch'","data":{"data":{"progress_action":"Initialized","server_name":"fetch","agent_name":"search_finder","tool_count":1,"prompt_count":"1","resource_count":0}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.894379","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2024-11-05","capabilities":{"experimental":null,"logging":null,"prompts":null,"resources":null,"tools":{"listChanged":null},"completions":null},"serverInfo":{"name":"g-search-mcp","title":null,"version":"0.1.0"},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.894457","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.895330","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.896575","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"search","title":null,"description":"Search on Google for multiple keywords and return the results","inputSchema":{"type":"object","properties":{"queries":{"type":"array","items":{"type":"string"},"description":"Array of search queries to perform"},"limit":{"type":"number","description":"Maximum number of results to return per query (default: 10)"},"timeout":{"type":"number","description":"Page loading timeout in milliseconds (default: 60000)"},"noSaveState":{"type":"boolean","description":"Whether to avoid saving browser state (default: false)"},"locale":{"type":"string","description":"Locale setting for search results (default: en-US)"},"debug":{"type":"boolean","description":"Whether to enable debug mode (showing browser window), overrides the --debug command line flag if specified"}},"required":["queries"]},"outputSchema":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.896985","namespace":"mcp_agent.mcp.mcp_aggregator.search_finder","message":"Server 'g-search' does not support prompts"}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.897171","namespace":"mcp_agent.mcp.mcp_aggregator.search_finder","message":"Server 'g-search' does not support resources"}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.897209","namespace":"mcp_agent.mcp.mcp_aggregator.search_finder","message":"MCP Aggregator initialized for server 'g-search'","data":{"data":{"progress_action":"Initialized","server_name":"g-search","agent_name":"search_finder","tool_count":1,"prompt_count":0,"resource_count":"0"}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.897723","namespace":"mcp_agent.agents.agent","message":"Agent search_finder initialized."}
{"level":"INFO","timestamp":"2025-09-17T08:38:36.897951","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.search_finder","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T08:38:36.930986","namespace":"mcp_agent.financial_websocket_research_test_user","message":"Financial session initialized for user test_user, type: research"}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.932297","namespace":"mcp_agent.agents.agent","message":"Human input callback not set"}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.932410","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.search_finder","message":"vLLM request arguments: {'model': 'Qwen/Qwen3-32B', 'messages': [{'role': 'system', 'content': 'Use Google Search to find information about OpenAI in the current month of May 2025:\\n    You are a world class research analyst.\\n    Execute these exact search queries:\\n    1. \"OpenAI stock price today\"\\n    2. \"OpenAI latest quarterly earnings\"\\n    3. \"OpenAI financial news\"\\n    4. \"OpenAI earnings expectations\"\\n    \\n    Extract the most relevant information about:\\n    - Current stock price and recent movement\\n    - Latest earnings report data\\n    - Any significant recent news with correct citations\\n    \\n    Be smart and concise. Keep responses short and focused on facts.'}, {'role': 'user', 'content': 'Please research the latest financial information for OpenAI'}], 'max_tokens': 4096, 'temperature': 0.7, 'stop': None, 'tools': [{'type': 'function', 'function': {'name': 'fetch_fetch', 'description': 'Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.', 'parameters': {'description': 'Parameters for fetching a URL.', 'properties': {'url': {'description': 'URL to fetch', 'format': 'uri', 'minLength': 1, 'title': 'Url', 'type': 'string'}, 'max_length': {'default': 5000, 'description': 'Maximum number of characters to return.', 'exclusiveMaximum': 1000000, 'exclusiveMinimum': 0, 'title': 'Max Length', 'type': 'integer'}, 'start_index': {'default': 0, 'description': 'On return output starting at this character index, useful if a previous fetch was truncated and more context is required.', 'minimum': 0, 'title': 'Start Index', 'type': 'integer'}, 'raw': {'default': False, 'description': 'Get the actual HTML content of the requested page, without simplification.', 'title': 'Raw', 'type': 'boolean'}}, 'required': ['url'], 'title': 'Fetch', 'type': 'object'}}}, {'type': 'function', 'function': {'name': 'g-search_search', 'description': 'Search on Google for multiple keywords and return the results', 'parameters': {'type': 'object', 'properties': {'queries': {'type': 'array', 'items': {'type': 'string'}, 'description': 'Array of search queries to perform'}, 'limit': {'type': 'number', 'description': 'Maximum number of results to return per query (default: 10)'}, 'timeout': {'type': 'number', 'description': 'Page loading timeout in milliseconds (default: 60000)'}, 'noSaveState': {'type': 'boolean', 'description': 'Whether to avoid saving browser state (default: false)'}, 'locale': {'type': 'string', 'description': 'Locale setting for search results (default: en-US)'}, 'debug': {'type': 'boolean', 'description': 'Whether to enable debug mode (showing browser window), overrides the --debug command line flag if specified'}}, 'required': ['queries']}}}], 'extra_body': {'chat_template_kwargs': {'enable_thinking': True}}}"}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:36.932430","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.search_finder","message":"Chat in progress","data":{"data":{"progress_action":"Chatting","model":"Qwen/Qwen3-32B","agent_name":"search_finder","chat_turn":1}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:43.293454","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Raw vLLM API response (asyncio mode):","data":{"data":{"id":"chatcmpl-679e1f954e194ee88a64139422865df6","choices":[{"finish_reason":"tool_calls","index":0,"logprobs":null,"message":{"content":"\n\n","refusal":null,"role":"assistant","annotations":null,"audio":null,"function_call":null,"tool_calls":[{"id":"chatcmpl-tool-6263c1bc570b4c3eb70c84ea5b7c4942","function":{"arguments":"{\"queries\": [\"OpenAI stock price today\", \"OpenAI latest quarterly earnings\", \"OpenAI financial news\", \"OpenAI earnings expectations\"], \"limit\": 5, \"locale\": \"en-US\"}","name":"g-search_search"},"type":"function"}],"reasoning_content":"\nOkay, the user wants the latest financial info on OpenAI. Let me start by figuring out what tools I have. There's the Google search function and the fetch function. The user provided specific search queries, so I should use the g-search_search function first with those queries.\n\nFirst, I need to execute the four search queries they listed. The queries are about stock price, quarterly earnings, financial news, and earnings expectations. I'll use the g-search_search function with those queries. The parameters should include the queries array, maybe a limit of 5 results per query to keep it concise. Then, after getting the search results, I can extract the relevant URLs and use fetch_fetch to get the actual content if needed. But since the user wants concise info, maybe the search results will have the necessary data. I'll check the top results for each query to find the most recent stock price movements, earnings reports, and any significant news. I'll need to make sure to cite the sources properly. If the search results don't have enough info, I'll use the fetch function on the URLs provided. Let me start with the Google searches first.\n"},"stop_reason":null}],"created":1758069516,"model":"Qwen/Qwen3-32B","object":"chat.completion","service_tier":null,"system_fingerprint":null,"usage":{"completion_tokens":293,"prompt_tokens":757,"total_tokens":1050,"completion_tokens_details":null,"prompt_tokens_details":null},"prompt_logprobs":null,"kv_transfer_params":null}}}
{"level":"INFO","timestamp":"2025-09-17T08:38:43.293506","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm","message":"Qwen Reasoning Content (choice 0):","data":{"data":{"reasoning":"\nOkay, the user wants the latest financial info on OpenAI. Let me start by figuring out what tools I have. There's the Google search function and the fetch function. The user provided specific search queries, so I should use the g-search_search function first with those queries.\n\nFirst, I need to execute the four search queries they listed. The queries are about stock price, quarterly earnings, financial news, and earnings expectations. I'll use the g-search_search function with those queries. The parameters should include the queries array, maybe a limit of 5 results per query to keep it concise. Then, after getting the search results, I can extract the relevant URLs and use fetch_fetch to get the actual content if needed. But since the user wants concise info, maybe the search results will have the necessary data. I'll check the top results for each query to find the most recent stock price movements, earnings reports, and any significant news. I'll need to make sure to cite the sources properly. If the search results don't have enough info, I'll use the fetch function on the URLs provided. Let me start with the Google searches first.\n"}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:43.298311","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.search_finder","message":"vLLM ChatCompletion response:","data":{"data":{"id":"chatcmpl-679e1f954e194ee88a64139422865df6","choices":[{"finish_reason":"tool_calls","index":0,"logprobs":null,"message":{"content":"\n\n","refusal":null,"role":"assistant","annotations":null,"audio":null,"function_call":null,"tool_calls":[{"id":"chatcmpl-tool-6263c1bc570b4c3eb70c84ea5b7c4942","function":{"arguments":"{\"queries\": [\"OpenAI stock price today\", \"OpenAI latest quarterly earnings\", \"OpenAI financial news\", \"OpenAI earnings expectations\"], \"limit\": 5, \"locale\": \"en-US\"}","name":"g-search_search"},"type":"function"}]}}],"created":1758069516,"model":"Qwen/Qwen3-32B","object":"chat.completion","service_tier":null,"system_fingerprint":null,"usage":{"completion_tokens":293,"prompt_tokens":757,"total_tokens":1050,"completion_tokens_details":null,"prompt_tokens_details":null},"_request_id":null}}}
{"level":"INFO","timestamp":"2025-09-17T08:38:43.300876","namespace":"mcp_agent.mcp.mcp_aggregator.search_finder","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"search","server_name":"g-search","agent_name":"search_finder"}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:38:43.301374","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"search","arguments":{"queries":["OpenAI stock price today","OpenAI latest quarterly earnings","OpenAI financial news","OpenAI earnings expectations"],"limit":5,"locale":"en-US"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T08:41:16.565225","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"{\n  \"searches\": [\n    {\n      \"query\": \"OpenAI stock price today\",\n      \"results\": [\n        {\n          \"title\": \"Search failed\",\n          \"link\": \"\",\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\nBrowser logs:\\n\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\n\u2551 Set either 'headless: true' or use 'xvfb-run <your-playwright-app>' before running Playwright. \u2551\\n\u2551                                                                                                \u2551\\n\u2551 <3 Playwright Team                                                                             \u2551\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\nCall log:\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-EWvjvv --remote-debugging-pipe --no-startup-window\\n  - <launched> pid=453327\\n  - [pid=453327][err] [453327:453340:0917/084115.584048:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\n  - [pid=453327][err] [453327:453327:0917/084115.584927:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\n  - [pid=453327][err] [453327:453327:0917/084115.584942:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\n\"\n        }\n      ]\n    },\n    {\n      \"query\": \"OpenAI latest quarterly earnings\",\n      \"results\": [\n        {\n          \"title\": \"Search failed\",\n          \"link\": \"\",\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\nBrowser logs:\\n\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\n\u2551 Set either 'headless: true' or use 'xvfb-run <your-playwright-app>' before running Playwright. \u2551\\n\u2551                                                                                                \u2551\\n\u2551 <3 Playwright Team                                                                             \u2551\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\nCall log:\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-pGUgvu --remote-debugging-pipe --no-startup-window\\n  - <launched> pid=453351\\n  - [pid=453351][err] [453351:453382:0917/084116.192738:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\n  - [pid=453351][err] [453351:453351:0917/084116.193869:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\n  - [pid=453351][err] [453351:453351:0917/084116.193888:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\n\"\n        }\n      ]\n    },\n    {\n      \"query\": \"OpenAI financial news\",\n      \"results\": [\n        {\n          \"title\": \"Search failed\",\n          \"link\": \"\",\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\nBrowser logs:\\n\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\n\u2551 Set either 'headless: true' or use 'xvfb-run <your-playwright-app>' before running Playwright. \u2551\\n\u2551                                                                                                \u2551\\n\u2551 <3 Playwright Team                                                                             \u2551\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\nCall log:\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-KlVe3v --remote-debugging-pipe --no-startup-window\\n  - <launched> pid=453349\\n  - [pid=453349][err] [453349:453369:0917/084116.187907:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\n  - [pid=453349][err] [453349:453349:0917/084116.189094:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\n  - [pid=453349][err] [453349:453349:0917/084116.189109:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\n\"\n        }\n      ]\n    },\n    {\n      \"query\": \"OpenAI earnings expectations\",\n      \"results\": [\n        {\n          \"title\": \"Search failed\",\n          \"link\": \"\",\n          \"snippet\": \"Unable to complete search, error message: browserType.launch: Target page, context or browser has been closed\\nBrowser logs:\\n\\n\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557\\n\u2551 Looks like you launched a headed browser without having a XServer running.                     \u2551\\n\u2551 Set either 'headless: true' or use 'xvfb-run <your-playwright-app>' before running Playwright. \u2551\\n\u2551                                                                                                \u2551\\n\u2551 <3 Playwright Team                                                                             \u2551\\n\u255a\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255d\\nCall log:\\n  - <launching> /root/.cache/ms-playwright/chromium-1179/chrome-linux/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --no-sandbox --disable-blink-features=AutomationControlled --disable-features=IsolateOrigins,site-per-process --disable-site-isolation-trials --disable-web-security --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu --hide-scrollbars --mute-audio --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-features=NetworkService,NetworkServiceInProcess --force-color-profile=srgb --metrics-recording-only --user-data-dir=/tmp/playwright_chromiumdev_profile-ioqgKu --remote-debugging-pipe --no-startup-window\\n  - <launched> pid=453396\\n  - [pid=453396][err] [453396:453409:0917/084116.526259:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Failed to connect to socket /var/run/dbus/system_bus_socket: No such file or directory\\n  - [pid=453396][err] [453396:453396:0917/084116.527247:ERROR:ui/ozone/platform/x11/ozone_platform_x11.cc:249] Missing X server or $DISPLAY\\n  - [pid=453396][err] [453396:453396:0917/084116.527263:ERROR:ui/aura/env.cc:257] The platform failed to initialize.  Exiting.\\n\"\n        }\n      ]\n    }\n  ]\n}","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
