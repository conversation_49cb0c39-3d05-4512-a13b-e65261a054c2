{"level":"INFO","timestamp":"2025-09-03T14:54:13.794999","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-03T14:54:13.796933","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-03T14:54:13.796965","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-03T14:54:13.796988","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-03T14:54:13.797007","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-03T14:54:13.797028","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.797048","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"b21cc282-2f84-4b6c-bcc8-2a638727348a"}}}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.869686","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.870834","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.871645","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.871687","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.871773","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.871788","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872094","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872301","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872376","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872391","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872401","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872416","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872433","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872443","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872458","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872466","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872477","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872485","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872498","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872612","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872623","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872637","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872646","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-09-03T14:54:13.872748","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-03T14:54:43.887880","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-03T14:54:43.888054","namespace":"mcp_agent.financial_websocket_orchestration_test_user","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-03T14:54:43.888078","namespace":"mcp_agent.financial_websocket_orchestration_test_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-03T14:54:43.888098","namespace":"mcp_agent.financial_websocket_orchestration_test_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-03T14:54:43.888116","namespace":"mcp_agent.financial_websocket_orchestration_test_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-03T14:54:43.888231","namespace":"mcp_agent.financial_websocket_orchestration_test_user","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-03T14:54:43.888252","namespace":"mcp_agent.financial_websocket_orchestration_test_user","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_orchestration_test_user","agent_name":"mcp_application_loop","session_id":"638ee191-32d7-489d-b23d-d89060b0da3c"}}}
{"level":"DEBUG","timestamp":"2025-09-03T14:54:43.888380","namespace":"mcp_agent.agents.agent","message":"Initializing agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-09-03T14:54:43.889028","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator initialized."}
{"level":"INFO","timestamp":"2025-09-03T14:54:43.889059","namespace":"mcp_agent.financial_websocket_orchestration_test_user","message":"Financial session initialized for user test_user, type: orchestration"}
{"level":"INFO","timestamp":"2025-09-03T14:54:43.892252","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_ff891279"}
{"level":"INFO","timestamp":"2025-09-03T14:54:43.892274","namespace":"orchestrator.orchestration_runner","message":"Query: Analyze shortage for order CUSTORD-202506001 for Tesla"}
{"level":"INFO","timestamp":"2025-09-03T14:54:43.892419","namespace":"orchestrator.query_processor","message":"Processing financial query: Analyze shortage for order CUSTORD-202506001 for Tesla..."}
{"level":"INFO","timestamp":"2025-09-03T14:54:43.898486","namespace":"orchestrator.query_processor","message":"Query classified as shortage_analysis (confidence: 0.08, workflow: mysql_shortage)"}
{"level":"INFO","timestamp":"2025-09-03T14:54:43.898515","namespace":"orchestrator.orchestration_runner","message":"Query parsed as shortage_analysis (confidence: 0.08)"}
{"level":"DEBUG","timestamp":"2025-09-03T14:54:43.899483","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_ff891279.json"}
{"level":"INFO","timestamp":"2025-09-03T14:54:43.899517","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_ff891279"}
{"level":"INFO","timestamp":"2025-09-03T14:54:43.899799","namespace":"orchestrator.orchestration_runner","message":"Executing orchestrator-based workflow for workflow_ff891279"}
{"level":"INFO","timestamp":"2025-09-03T14:54:43.900122","namespace":"orchestrator.financial_orchestrator","message":"Starting financial analysis for query: Analyze shortage for order CUSTORD-202506001 for Tesla"}
{"level":"INFO","timestamp":"2025-09-03T14:54:43.901257","namespace":"orchestrator.financial_orchestrator","message":"Parsed query type: shortage, workflow: shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-03T14:54:43.901288","namespace":"orchestrator.financial_orchestrator","message":"Executing shortage analysis workflow"}
{"level":"INFO","timestamp":"2025-09-03T14:54:43.901300","namespace":"orchestrator.financial_orchestrator","message":"Executing MySQL analysis step"}
{"level":"INFO","timestamp":"2025-09-03T14:54:50.672976","namespace":"orchestrator.financial_orchestrator","message":"Executing MySQL tool: mysql_agent (iteration 1)"}
{"level":"INFO","timestamp":"2025-09-03T14:54:58.595923","namespace":"orchestrator.financial_orchestrator","message":"Executing MySQL tool: mysql_agent (iteration 2)"}
{"level":"INFO","timestamp":"2025-09-03T14:55:05.367121","namespace":"orchestrator.financial_orchestrator","message":"Executing MySQL tool: mysql_agent (iteration 3)"}
{"level":"INFO","timestamp":"2025-09-03T14:55:13.740301","namespace":"orchestrator.financial_orchestrator","message":"Executing MySQL tool: mysql_agent (iteration 4)"}
{"level":"INFO","timestamp":"2025-09-03T14:55:21.589247","namespace":"orchestrator.financial_orchestrator","message":"Executing MySQL tool: mysql_agent (iteration 5)"}
{"level":"INFO","timestamp":"2025-09-03T14:55:33.535313","namespace":"orchestrator.financial_orchestrator","message":"Executing MySQL tool: mysql_agent (iteration 6)"}
{"level":"INFO","timestamp":"2025-09-03T14:55:54.604192","namespace":"orchestrator.financial_orchestrator","message":"\u2713 MySQL analysis completed successfully"}
{"level":"INFO","timestamp":"2025-09-03T14:55:54.604332","namespace":"orchestrator.financial_orchestrator","message":"Executing shortage analysis step"}
{"level":"INFO","timestamp":"2025-09-03T14:55:54.611375","namespace":"orchestrator.financial_orchestrator","message":"\u2713 Shortage analysis completed successfully"}
{"level":"INFO","timestamp":"2025-09-03T14:55:54.611430","namespace":"orchestrator.financial_orchestrator","message":"Executing alert management step"}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.614978","namespace":"mcp_agent.agents.agent","message":"Initializing agent alert_manager_websocketorchestration..."}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.616848","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Found server configuration=","data":{"data":{"name":"alert-notification","description":null,"transport":"sse","command":null,"args":[],"url":"http://localhost:6972/sse","headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"INFO","timestamp":"2025-09-03T14:55:54.617432","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Up and running with a persistent connection!"}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.659878","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0"}}}}}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.664751","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":{},"logging":null,"prompts":{"listChanged":false},"resources":{"subscribe":"False","listChanged":"False"},"tools":{"listChanged":"False"},"completions":null},"serverInfo":{"name":"example-mcp-server","title":null,"version":"1.11.0"},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.664846","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.666011","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.673693","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"TestTool","title":null,"description":"Simple test tool.","inputSchema":{"properties":{"message":{"description":"Test message","title":"Message","type":"string"}},"required":["message"],"title":"TestToolArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"HttpNotification","title":null,"description":"Sends an HTTP alert to a specified endpoint.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the notification","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the notification message","title":"Content","type":"string"}},"required":["alert_message"],"title":"HttpNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"MqttNotification","title":null,"description":"Sends an MQTT alert to a specified topic.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the alert","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the alert message","title":"Content","type":"string"}},"required":["alert_message"],"title":"MqttNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"EmailNotification","title":null,"description":"Sends an email notification.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the email","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the email message","title":"Content","type":"string"}},"required":["alert_message"],"title":"EmailNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.674487","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"prompts/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.680214","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"prompts":[]}}}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.680651","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"resources/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.686076","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"resources":[]}}}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.686158","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"MCP Aggregator initialized for server 'alert-notification'","data":{"data":{"progress_action":"Initialized","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration","tool_count":4,"prompt_count":0,"resource_count":"0"}}}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.686623","namespace":"mcp_agent.agents.agent","message":"Agent alert_manager_websocketorchestration initialized."}
{"level":"INFO","timestamp":"2025-09-03T14:55:54.687326","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.687720","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.698594","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-03T14:55:54.699163","namespace":"orchestrator.financial_orchestrator","message":"\u2713 Alert management completed successfully"}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.699782","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_ff891279.json"}
{"level":"INFO","timestamp":"2025-09-03T14:55:54.699817","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_ff891279"}
{"level":"INFO","timestamp":"2025-09-03T14:55:54.699842","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_ff891279 (success: True, time: 70.81s)"}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.701071","namespace":"mcp_agent.agents.agent","message":"Shutting down agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.701256","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Decremented connection ref count to 1"}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.701277","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Aggregator closing with ref count 1, connection manager will remain active"}
{"level":"DEBUG","timestamp":"2025-09-03T14:55:54.701310","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator shutdown."}
{"level":"INFO","timestamp":"2025-09-03T14:55:54.701342","namespace":"mcp_agent.financial_websocket_orchestration_test_user","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_orchestration_test_user","agent_name":"mcp_application_loop"}}}
