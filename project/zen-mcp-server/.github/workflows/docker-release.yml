name: Docker Release Build

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      tag:
        description: 'Tag to build (leave empty for latest release)'
        required: false
        type: string

permissions:
  contents: read
  packages: write

jobs:
  docker:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          # If triggered by workflow_dispatch with a tag, checkout that tag
          ref: ${{ inputs.tag || github.event.release.tag_name }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository }}
          tags: |
            # Tag with the release version
            type=semver,pattern={{version}},value=${{ inputs.tag || github.event.release.tag_name }}
            type=semver,pattern={{major}}.{{minor}},value=${{ inputs.tag || github.event.release.tag_name }}
            type=semver,pattern={{major}},value=${{ inputs.tag || github.event.release.tag_name }}
            # Also tag as latest for the most recent release
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Update release with Docker info
        if: github.event_name == 'release'
        run: |
          RELEASE_TAG="${{ github.event.release.tag_name }}"
          DOCKER_TAGS=$(echo "${{ steps.meta.outputs.tags }}" | tr '\n' ' ')
          
          # Add Docker information to the release
          gh release edit "$RELEASE_TAG" --notes-file - << EOF
          ${{ github.event.release.body }}
          
          ---
          
          ## 🐳 Docker Images
          
          This release is available as Docker images:
          
          $(echo "$DOCKER_TAGS" | sed 's/ghcr.io/- `ghcr.io/g' | sed 's/ /`\n/g')
          
          **Quick start with Docker:**
          \`\`\`bash
          docker pull ghcr.io/${{ github.repository }}:$RELEASE_TAG
          \`\`\`
          
          **Claude Desktop configuration:**
          \`\`\`json
          {
            "mcpServers": {
              "zen-mcp-server": {
                "command": "docker",
                "args": [
                  "run", "--rm", "-i",
                  "-e", "GEMINI_API_KEY",
                  "ghcr.io/${{ github.repository }}:$RELEASE_TAG"
                ],
                "env": {
                  "GEMINI_API_KEY": "your-api-key-here"
                }
              }
            }
          }
          \`\`\`
          EOF
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Create deployment summary
        run: |
          echo "## 🐳 Docker Release Build Complete" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Release**: ${{ inputs.tag || github.event.release.tag_name }}" >> $GITHUB_STEP_SUMMARY
          echo "**Images built:**" >> $GITHUB_STEP_SUMMARY
          echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
          echo "${{ steps.meta.outputs.tags }}" >> $GITHUB_STEP_SUMMARY
          echo "\`\`\`" >> $GITHUB_STEP_SUMMARY