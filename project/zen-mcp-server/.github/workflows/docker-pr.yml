name: PR Docker Build

on:
  pull_request:
    types: [opened, synchronize, reopened, labeled, unlabeled]
    paths:
      - '**.py'
      - 'requirements*.txt'
      - 'pyproject.toml'
      - 'Dockerfile'
      - 'docker-compose.yml'
      - '.dockerignore'

permissions:
  contents: read
  packages: write
  pull-requests: write

jobs:
  docker:
    name: Build Docker Image
    runs-on: ubuntu-latest
    if: |
      github.event.action == 'opened' ||
      github.event.action == 'synchronize' ||
      github.event.action == 'reopened' ||
      contains(github.event.pull_request.labels.*.name, 'docker-build')
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        if: github.event.pull_request.head.repo.full_name == github.repository
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository }}
          tags: |
            # PR-specific tag for testing
            type=raw,value=pr-${{ github.event.number }}-${{ github.sha }}
            type=raw,value=pr-${{ github.event.number }}

      - name: Build and push Docker image (internal PRs)
        if: github.event.pull_request.head.repo.full_name == github.repository
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build Docker image (fork PRs)
        if: github.event.pull_request.head.repo.full_name != github.repository
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: false
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Add Docker build comment (internal PRs)
        if: github.event.pull_request.head.repo.full_name == github.repository
        uses: marocchino/sticky-pull-request-comment@d2ad0de260ae8b0235ce059e63f2949ba9e05943 # v2.9.3
        with:
          header: docker-build
          message: |
            ## 🐳 Docker Build Complete
            
            **PR**: #${{ github.event.number }} | **Commit**: `${{ github.sha }}`
            
            ```
            ${{ steps.meta.outputs.tags }}
            ```
            
            **Test:** `docker pull ghcr.io/${{ github.repository }}:pr-${{ github.event.number }}`
            
            **Claude config:**
            ```json
            {
              "mcpServers": {
                "zen": {
                  "command": "docker",
                  "args": ["run", "--rm", "-i", "-e", "GEMINI_API_KEY", "ghcr.io/${{ github.repository }}:pr-${{ github.event.number }}"],
                  "env": { "GEMINI_API_KEY": "your-key" }
                }
              }
            }
            ```
            
            💡 Add `docker-build` label to manually trigger builds


      - name: Update job summary (internal PRs)
        if: github.event.pull_request.head.repo.full_name == github.repository
        run: |
          {
            echo "## 🐳 Docker Build Complete"
            echo "**PR**: #${{ github.event.number }} | **Commit**: ${{ github.sha }}"
            echo '```'
            echo "${{ steps.meta.outputs.tags }}"
            echo '```'
          } >> $GITHUB_STEP_SUMMARY

      - name: Update job summary (fork PRs)
        if: github.event.pull_request.head.repo.full_name != github.repository
        run: |
          {
            echo "## 🐳 Docker Build Complete (Build Only)"
            echo "**PR**: #${{ github.event.number }} | **Commit**: ${{ github.sha }}"
            echo "✅ Multi-platform Docker build successful"
            echo "Note: Fork PRs only build (no push) for security"
          } >> $GITHUB_STEP_SUMMARY
