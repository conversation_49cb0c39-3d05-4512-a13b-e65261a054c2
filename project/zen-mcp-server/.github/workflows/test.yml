name: Tests

on:
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12"]

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt

      - name: Run unit tests
        run: |
          # Run only unit tests (exclude simulation tests and integration tests)
          # Integration tests require local-llama which isn't available in CI
          python -m pytest tests/ -v --ignore=simulator_tests/ -m "not integration"
        env:
          # Ensure no API key is accidentally used in CI
          GEMINI_API_KEY: ""
          OPENAI_API_KEY: ""

  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements-dev.txt

      - name: Run black formatter check
        run: black --check . --exclude="test_simulation_files/"

      - name: Run ruff linter
        run: ruff check . --exclude test_simulation_files
