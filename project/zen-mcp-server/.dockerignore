# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
.zen_venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/*.log*
*.log

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
docs/
README.md
*.md

# Tests
tests/
simulator_tests/
test_simulation_files/
pytest.ini

# Development
.env
.env.local
examples/
code_quality_checks.sh
run_integration_tests.sh

# Security - Sensitive files
*.key
*.pem
*.p12
*.pfx
*.crt
*.csr
secrets/
private/
