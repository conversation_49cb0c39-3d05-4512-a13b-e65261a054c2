#!/usr/bin/env python3
"""Minimal FastMCP test to isolate framework issues."""

import uvicorn
from mcp.server.fastmcp import FastMCP
from mcp.server.sse import SseServerTransport
from starlette.applications import Starlette
from starlette.routing import Mount, Route
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware

# Create minimal FastMCP server
mcp = FastMCP("test-server")

@mcp.tool(name="SimpleTest", description="Simple test tool")
async def simple_test(message: str) -> str:
    """Simple test that just returns a message."""
    print(f"SimpleTest called with: {message}")
    return f"Received: {message}"

# Get the MCP server
mcp_server = mcp._mcp_server

def create_starlette_app(mcp_server) -> Starlette:
    """Create minimal Starlette app with SSE."""
    sse = SseServerTransport("/messages/")

    async def handle_sse(request):
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,
        ) as (read_stream, write_stream):
            await mcp_server.run(
                read_stream,
                write_stream,
                mcp_server.create_initialization_options(),
            )

    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
            allow_credentials=True,
        )
    ]

    return Starlette(
        routes=[
            Route("/sse", endpoint=handle_sse),
            Mount("/messages/", app=sse.handle_post_message),
        ],
        middleware=middleware,
    )

app = create_starlette_app(mcp_server)

if __name__ == "__main__":
    print("Starting minimal FastMCP server on port 6974...")
    uvicorn.run(
        "test_minimal_fastmcp:app",
        host="0.0.0.0",
        port=6974,
        reload=False
    )