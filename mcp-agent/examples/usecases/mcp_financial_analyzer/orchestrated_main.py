"""
Orchestrated Financial Analysis Tool
===================================

Main entry point for the orchestrated financial analysis system that coordinates
MySQL Agent, Shortage Analyzer Agent, and Alert Manager Agent through an
intelligent orchestration layer.

Features:
- Intelligent query parsing and workflow routing
- Context-aware agent coordination
- Error handling and recovery
- Performance monitoring and statistics
- Comprehensive workflow patterns
- Comprehensive LLM debug logging with reasoning capture

Debug Logging Features:
- LLM reasoning process capture
- Complete LLM output responses
- Agent-specific interaction tracking
- Orchestration workflow detailed logging
- Performance timing measurements
- Structured debug logs with timestamps
- Debug log file output (orchestrated_main_debug.log)
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, Any, Optional

from mcp_agent.app import MC<PERSON><PERSON>
from mcp_agent.workflows.llm.augmented_llm import AugmentedLLM
from mcp_agent.agents.agent import Agent

# Import orchestration components
from orchestrator.orchestration_runner import create_orchestration_runner, OrchestrationRunner
from agents.agent_interfaces import AgentOrchestrationManager

# Import existing agents
from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
from agents.alert_manager_agent import create_alert_manager_agent  
from agents.mysql_agent import create_mysql_orchestrator_agent

# Server management from existing main
from main import DualServerManager

# Set up comprehensive logging with debug support
def setup_comprehensive_logging(show_llm_realtime=True):
    """Set up comprehensive logging with LLM debug support and log file management.

    Args:
        show_llm_realtime: If True, display LLM reasoning and output in real-time on console
    """
    # Ensure logs directory exists
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)

    # Create timestamped log file for this session
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    debug_log_file = logs_dir / f"orchestrated_main_debug_{timestamp}.log"

    # Configure root logger
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(name)s - [%(filename)s:%(lineno)d] - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(debug_log_file, mode='w')
        ]
    )

    # Create specialized LLM debug logger with enhanced formatting
    llm_debug_logger = logging.getLogger('llm_debug')
    llm_debug_logger.setLevel(logging.DEBUG)

    # Create dedicated LLM debug file handler
    llm_debug_file = logs_dir / f"llm_reasoning_debug_{timestamp}.log"
    llm_file_handler = logging.FileHandler(llm_debug_file, mode='w')
    llm_file_handler.setLevel(logging.DEBUG)
    llm_file_handler.setFormatter(logging.Formatter(
        '%(asctime)s - LLM_DEBUG - %(message)s'
    ))
    llm_debug_logger.addHandler(llm_file_handler)

    # Add console handler for real-time LLM debug display if requested
    if show_llm_realtime:
        llm_console_handler = logging.StreamHandler(sys.stdout)
        llm_console_handler.setLevel(logging.DEBUG)
        # Use a more concise format for console display
        llm_console_handler.setFormatter(logging.Formatter(
            '\n🧠 %(message)s'
        ))
        llm_debug_logger.addHandler(llm_console_handler)

        print("🔍 Real-time LLM debug logging enabled - you'll see LLM reasoning and outputs as they happen")

    return debug_log_file, llm_debug_file

# Initialize logging (will be configured in main() based on command line args)
debug_log_file = None
llm_debug_file = None
logger = None

# Create specialized LLM debug logger
llm_debug_logger = logging.getLogger('llm_debug')

# Initialize MCP App using the config in this example directory so server registry is correct
app = MCPApp(
    name="orchestrated_financial_analyzer",
    human_input_callback=None,
    settings=str(Path(__file__).with_name("mcp_agent.config.yaml"))
)


def log_llm_interaction(interaction_type: str, agent_name: str = "unknown",
                       query: str = "", response: str = "", reasoning: str = "",
                       execution_time: float = 0.0, extra_data: Dict[str, Any] = None,
                       thinking_content: str = "", raw_completion: str = ""):
    """Enhanced LLM debug logging with comprehensive reasoning and output capture."""
    from datetime import datetime
    separator = "=" * 80
    timestamp = datetime.now().isoformat()

    # Create detailed log entry for file
    detailed_log = []
    detailed_log.append(f"\n{separator}")
    detailed_log.append(f"🧠 LLM INTERACTION: {interaction_type.upper()}")
    detailed_log.append(f"Agent: {agent_name}")
    detailed_log.append(f"Timestamp: {timestamp}")
    detailed_log.append(f"Execution Time: {execution_time:.3f}s")

    if query:
        detailed_log.append(f"\n📝 INPUT QUERY:")
        detailed_log.append(f"{query}")

    if thinking_content:
        detailed_log.append(f"\n🤔 LLM THINKING PROCESS (Qwen Reasoning):")
        detailed_log.append(f"{thinking_content}")

    if reasoning:
        detailed_log.append(f"\n� LLM REASONING PROCESS:")
        detailed_log.append(f"{reasoning}")

    if raw_completion:
        detailed_log.append(f"\n🔍 RAW LLM COMPLETION:")
        detailed_log.append(f"{raw_completion}")

    if response:
        detailed_log.append(f"\n💭 LLM OUTPUT RESPONSE:")
        detailed_log.append(f"{response}")

    if extra_data:
        detailed_log.append(f"\n📊 ADDITIONAL DATA:")
        for key, value in extra_data.items():
            if isinstance(value, (dict, list)):
                import json
                try:
                    formatted_value = json.dumps(value, indent=2, default=str)
                    detailed_log.append(f"  {key}:\n{formatted_value}")
                except:
                    detailed_log.append(f"  {key}: {value}")
            else:
                detailed_log.append(f"  {key}: {value}")

    detailed_log.append(f"{separator}\n")

    # Log the detailed version (goes to both file and console if real-time is enabled)
    for line in detailed_log:
        llm_debug_logger.debug(line)


def create_log_summary_report():
    """Create a comprehensive summary report of all debug logs."""
    try:
        logs_dir = Path("logs")
        if not logs_dir.exists():
            logger.warning("Logs directory does not exist")
            return

        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_file = logs_dir / f"debug_log_summary_{timestamp}.md"

        # Find all debug log files from this session
        debug_files = list(logs_dir.glob("*debug*.log"))
        llm_files = list(logs_dir.glob("*reasoning*.log"))

        with open(summary_file, 'w') as f:
            f.write(f"# Debug Log Summary Report\n")
            f.write(f"Generated: {datetime.now().isoformat()}\n\n")

            f.write(f"## Session Files\n")
            f.write(f"- Main Debug Log: {debug_log_file}\n")
            f.write(f"- LLM Reasoning Log: {llm_debug_file}\n\n")

            f.write(f"## Available Log Files\n")
            for log_file in debug_files + llm_files:
                f.write(f"- {log_file.name}\n")

            f.write(f"\n## Log File Locations\n")
            f.write(f"All logs are stored in: `{logs_dir.absolute()}`\n\n")

            f.write(f"## Usage Instructions\n")
            f.write(f"1. **Main Debug Log**: Contains all system operations and agent interactions\n")
            f.write(f"2. **LLM Reasoning Log**: Contains detailed LLM reasoning processes and outputs\n")
            f.write(f"3. **Summary Report**: This file provides an overview of the logging session\n\n")

            f.write(f"## Key Features Logged\n")
            f.write(f"- LLM reasoning processes (including Qwen thinking mode)\n")
            f.write(f"- Complete LLM output responses\n")
            f.write(f"- Agent-specific interaction tracking\n")
            f.write(f"- Orchestration workflow detailed logging\n")
            f.write(f"- Performance timing measurements\n")
            f.write(f"- Structured debug logs with timestamps\n")

        logger.info(f"Debug log summary report created: {summary_file}")
        return summary_file

    except Exception as e:
        logger.error(f"Failed to create log summary report: {e}")
        return None


class OrchestrationDemo:
    """Demo class for orchestrated financial analysis workflows."""
    
    def __init__(self):
        """Initialize the orchestration demo."""
        self.server_manager = DualServerManager()
        self.orchestration_runner: Optional[OrchestrationRunner] = None
        self.agent_manager: Optional[AgentOrchestrationManager] = None
        
    async def initialize_system(self) -> bool:
        """Initialize the complete orchestration system."""
        logger.info("=== Initializing Orchestrated Financial Analysis System ===")
        
        # Step 1: Check server availability
        logger.info("Checking MCP server availability...")
        shortage_available, alert_available, mysql_available = await self.server_manager.check_all_servers_health()
        
        if not all([shortage_available, alert_available, mysql_available]):
            missing_servers = []
            if not shortage_available:
                missing_servers.append("shortage-index (6970)")
            if not alert_available:
                missing_servers.append("alert-notification (6972)")
            if not mysql_available:
                missing_servers.append("mysql (8702)")
            
            logger.error(f"Required MCP servers not available: {', '.join(missing_servers)}")
            self.server_manager.display_startup_instructions(
                shortage_needed=not shortage_available,
                alert_needed=not alert_available,
                mysql_needed=not mysql_available
            )
            return False
        
        logger.info("✓ All MCP servers are available")
        
        # Step 2: Initialize MCP App context
        logger.info("Initializing MCP App context...")
        async with app.run() as analyzer_app:
            context = analyzer_app.context
            logger.info("✓ MCP App context initialized")
            
            # Step 3: Create agents
            logger.info("Creating and initializing agents...")
            mysql_agent = create_mysql_orchestrator_agent()
            shortage_agent = create_shortage_analyzer_agent("OrchestrationDemo")
            # Configure alert manager to trigger on medium shortages in demo and use MQTT/HTTP channels
            alert_agent = create_alert_manager_agent(
                "OrchestrationDemo",
                alert_config={
                    "shortage_threshold": 0.55,
                    "enabled_channels": ["http"],
                }
            )
            
            # Step 4: Initialize agents for MCP
            agents_to_init = [
                ("MySQL", mysql_agent),
                ("Shortage", shortage_agent), 
                ("Alert", alert_agent)
            ]
            
            for agent_name, agent in agents_to_init:
                if hasattr(agent, 'initialize_llm'):
                    try:
                        await agent.initialize_llm()
                        logger.info(f"✓ {agent_name} agent LLM initialized")
                    except Exception as e:
                        logger.error(f"✗ {agent_name} agent initialization failed: {e}")
                        return False
                else:
                    logger.warning(f"✗ {agent_name} agent does not support LLM initialization")
            
            # Step 5: Create LLM factory for orchestrator with debug logging
            def llm_factory(agent: Agent) -> AugmentedLLM:
                """Factory function to create LLM instances with comprehensive debug logging."""
                from mcp_agent.workflows.llm.augmented_llm_vllm import VLLMAugmentedLLM

                llm_debug_logger.debug(f"🏭 Creating LLM instance for agent: {agent.name}")
                llm_debug_logger.debug(f"📋 Agent type: {type(agent).__name__}")

                try:
                    # Try to use the same LLM configuration as the agents
                    llm_instance = VLLMAugmentedLLM(agent=agent, context=context)

                    # Wrap the LLM to capture reasoning and responses
                    original_generate = llm_instance.generate
                    original_generate_str = llm_instance.generate_str

                    async def debug_generate(*args, **kwargs):
                        import time
                        start_time = time.time()

                        # Extract input details for logging
                        input_query = ""
                        if args:
                            if hasattr(args[0], 'content'):
                                input_query = str(args[0].content)
                            elif hasattr(args[0], 'messages'):
                                input_query = str(args[0].messages)
                            else:
                                input_query = str(args[0])

                        # Check for thinking mode in kwargs
                        enable_thinking = kwargs.get('enable_thinking', False)

                        log_llm_interaction(
                            "GENERATE_START",
                            agent.name,
                            query=input_query,
                            extra_data={
                                "method": "generate",
                                "args_count": len(args),
                                "enable_thinking": enable_thinking,
                                "kwargs_keys": list(kwargs.keys())
                            }
                        )

                        try:
                            result = await original_generate(*args, **kwargs)
                            execution_time = time.time() - start_time

                            # Extract comprehensive result information
                            reasoning = ""
                            thinking_content = ""
                            raw_completion = ""
                            response = ""

                            if result:
                                # Try to extract reasoning from various possible attributes
                                reasoning = getattr(result, 'reasoning', '') or getattr(result, 'chain_of_thought', '')
                                thinking_content = getattr(result, 'thinking', '') or getattr(result, 'thought_process', '')
                                raw_completion = getattr(result, 'raw_response', '') or getattr(result, 'completion', '')

                                # Handle different result types
                                if hasattr(result, 'content'):
                                    response = str(result.content)
                                elif hasattr(result, 'message'):
                                    response = str(result.message)
                                else:
                                    response = str(result)

                            log_llm_interaction(
                                "GENERATE_COMPLETE",
                                agent.name,
                                response=response,
                                reasoning=reasoning,
                                thinking_content=thinking_content,
                                raw_completion=raw_completion,
                                execution_time=execution_time,
                                extra_data={
                                    "result_type": type(result).__name__,
                                    "has_reasoning": bool(reasoning),
                                    "has_thinking": bool(thinking_content),
                                    "response_length": len(response) if response else 0
                                }
                            )

                            return result
                        except Exception as e:
                            execution_time = time.time() - start_time
                            log_llm_interaction(
                                "GENERATE_ERROR",
                                agent.name,
                                response=f"ERROR: {str(e)}",
                                execution_time=execution_time,
                                extra_data={
                                    "error_type": type(e).__name__,
                                    "error_details": str(e)
                                }
                            )
                            raise
                    
                    async def debug_generate_str(*args, **kwargs):
                        import time
                        start_time = time.time()

                        # Extract input details for logging
                        input_query = ""
                        if args:
                            if isinstance(args[0], str):
                                input_query = args[0]
                            elif hasattr(args[0], 'content'):
                                input_query = str(args[0].content)
                            else:
                                input_query = str(args[0])

                        # Check for thinking mode in kwargs
                        enable_thinking = kwargs.get('enable_thinking', False)

                        log_llm_interaction(
                            "GENERATE_STR_START",
                            agent.name,
                            query=input_query,
                            extra_data={
                                "method": "generate_str",
                                "args_count": len(args),
                                "enable_thinking": enable_thinking,
                                "kwargs_keys": list(kwargs.keys())
                            }
                        )

                        try:
                            result = await original_generate_str(*args, **kwargs)
                            execution_time = time.time() - start_time

                            # For string results, try to extract thinking content if available
                            thinking_content = ""
                            raw_completion = ""
                            response = result if isinstance(result, str) else str(result)

                            # If thinking mode was enabled, try to parse thinking content
                            if enable_thinking and result and "<thinking>" in str(result):
                                import re
                                thinking_match = re.search(r'<thinking>(.*?)</thinking>', str(result), re.DOTALL)
                                if thinking_match:
                                    thinking_content = thinking_match.group(1).strip()
                                    # Extract the actual response after thinking
                                    response = re.sub(r'<thinking>.*?</thinking>', '', str(result), flags=re.DOTALL).strip()

                            log_llm_interaction(
                                "GENERATE_STR_COMPLETE",
                                agent.name,
                                response=response,
                                thinking_content=thinking_content,
                                raw_completion=str(result) if thinking_content else "",
                                execution_time=execution_time,
                                extra_data={
                                    "response_length": len(response) if response else 0,
                                    "has_thinking": bool(thinking_content),
                                    "raw_length": len(str(result)) if result else 0
                                }
                            )

                            return result
                        except Exception as e:
                            execution_time = time.time() - start_time
                            log_llm_interaction(
                                "GENERATE_STR_ERROR",
                                agent.name,
                                response=f"ERROR: {str(e)}",
                                execution_time=execution_time,
                                extra_data={
                                    "error_type": type(e).__name__,
                                    "error_details": str(e)
                                }
                            )
                            raise
                    
                    # Replace methods with debug versions
                    llm_instance.generate = debug_generate
                    llm_instance.generate_str = debug_generate_str
                    
                    llm_debug_logger.debug(f"✅ LLM instance created successfully for {agent.name}")
                    return llm_instance
                    
                except Exception as e:
                    # Fallback to basic configuration
                    logger.warning(f"Using fallback LLM configuration for {agent.name}: {e}")
                    llm_debug_logger.debug(f"⚠️ LLM factory fallback for {agent.name}: {str(e)}")
                    return VLLMAugmentedLLM(agent=agent, context=context)
            
            # Step 6: Create orchestration runner
            logger.info("Creating orchestration runner...")
            self.orchestration_runner = create_orchestration_runner(
                mysql_agent=mysql_agent,
                shortage_agent=shortage_agent,
                alert_agent=alert_agent,
                llm_factory=llm_factory,
                persist_context=True
            )
            
            # Step 7: Create agent manager for interface testing
            self.agent_manager = AgentOrchestrationManager()
            self.agent_manager.register_agent("mysql_analyzer", mysql_agent)
            self.agent_manager.register_agent("shortage_analyzer", shortage_agent)
            self.agent_manager.register_agent("alert_manager", alert_agent)
            
            # Initialize agent interfaces
            await self.agent_manager.initialize_all_agents()
            
            logger.info("✓ Orchestration system initialized successfully")
            return True
    
    async def run_health_check(self) -> Dict[str, Any]:
        """Run comprehensive health check of the orchestration system."""
        if not self.orchestration_runner:
            return {"status": "not_initialized", "error": "System not initialized"}
        
        logger.info("\n=== Orchestration System Health Check ===")
        health = await self.orchestration_runner.health_check()
        
        # Display health status
        logger.info(f"System Status: {health['status']}")
        logger.info(f"Components: {len(health['components'])}")
        
        for component, status in health.get('components', {}).items():
            if isinstance(status, dict):
                comp_status = status.get('status', 'unknown')
                logger.info(f"  {component}: {comp_status}")
            else:
                logger.info(f"  {component}: {status}")
        
        if health['status'] != 'healthy':
            logger.warning("⚠️ System health issues detected")
            if 'issues' in health:
                for issue in health['issues']:
                    logger.warning(f"  - {issue}")
        else:
            logger.info("✓ All systems healthy")
        
        return health
    
    async def run_demo_scenarios(self, scenario_num: Optional[int] = None) -> None:
        """Run demonstration scenarios using the orchestrator.
        
        Args:
            scenario_num: Optional scenario number (1-4). If None, runs all scenarios.
        """
        if not self.orchestration_runner:
            logger.error("Orchestration system not initialized")
            return

        if scenario_num is not None:
            if scenario_num < 1 or scenario_num > 4:
                logger.error(f"Invalid scenario number: {scenario_num}. Must be 1-4.")
                return
            await self._run_single_demo_scenario(scenario_num)
        else:
            logger.info("\n=== Running All Orchestration Demo Scenarios ===")
            results = []
            for i in range(1, 5):
                result = await self._run_single_demo_scenario(i)
                results.append(result)
            
            # Summary
            self._display_demo_summary(results)
    
    async def _run_single_demo_scenario(self, scenario_num: int) -> Dict[str, Any]:
        """Run a single demo scenario by number.
        
        Args:
            scenario_num: Scenario number (1-4)
            
        Returns:
            Dict containing the execution result
        """
        scenarios = {
            1: {
                "name": "CRITICAL Database Shortage Analysis with Customer Alert",
                "query": "CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION.",
                "description": "Triggers MySQL → Shortage Analysis → Alert Manager pipeline"
            },
            2: {
                "name": "Supplier Risk Alert with SLA Breach Notification", 
                "query": "Query database for MetaMind Technology Co., Ltd. inventory levels DEP2004IC001/MM2004IC001. Calculate supplier risk for 500 CPUs, 200 GPUs delivery. If shortage detected, ESCALATE IMMEDIATELY to management and SEND CRITICAL ALERT to customer regarding SLA BREACH risk.",
                "description": "Tests database → analysis → customer notification workflow"
            },
            3: {
                "name": "URGENT Customer Priority Alert Analysis",
                "query": "URGENT: Query database for WO-202506001 component availability (HCS500D001, MM2004IC001, ATR6G00801). Calculate shortage risk and if CRITICAL, send IMMEDIATE CUSTOMER NOTIFICATION to Tech Pioneer Co., Ltd. with Net 30 payment escalation.",
                "description": "Database check → shortage analysis → priority customer alerts"
            },
            4: {
                "name": "Multi-Order Critical Alert Orchestration",
                "query": "CRITICAL MULTI-ORDER ANALYSIS: Query database for WO-202506001/002/003 inventory (DEP2004IC001, MM2004IC001, ATR6G00801, MS300PC801). Calculate weighted shortage indices and TRIGGER IMMEDIATE ALERTS for any CRITICAL shortages with CUSTOMER ESCALATION and management notification.",
                "description": "Full pipeline: database → analysis → alert orchestration"
            }
        }
        
        scenario = scenarios[scenario_num]
        
        logger.info(f"\n--- Demo Scenario {scenario_num}: {scenario['name']} ---")
        logger.info(f"Description: {scenario['description']}")
        logger.info(f"Query: {scenario['query']}")
        
        try:
            # Log orchestration start with full details
            log_llm_interaction(
                "ORCHESTRATION_START",
                "OrchestrationRunner",
                query=scenario['query'],
                extra_data={
                    "scenario_num": scenario_num,
                    "scenario_name": scenario['name'],
                    "execution_mode": "pattern_based"
                }
            )
            
            import time
            orchestration_start_time = time.time()
            
            # Execute using pattern-based orchestration
            result = await self.orchestration_runner.execute_financial_query(
                query=scenario['query'],
                execution_mode="pattern_based"
            )
            
            orchestration_execution_time = time.time() - orchestration_start_time
            
            # Log orchestration completion with detailed results
            log_llm_interaction(
                "ORCHESTRATION_COMPLETE",
                "OrchestrationRunner",
                response=str(result.get('final_response', 'No final response')) if result else 'No result',
                execution_time=orchestration_execution_time,
                extra_data={
                    "scenario_num": scenario_num,
                    "scenario_name": scenario['name'],
                    "success": result.get('success', False) if result else False,
                    "workflow_id": result.get('workflow_id', 'unknown') if result else 'unknown',
                    "query_analysis": result.get('query_analysis', {}) if result else {},
                    "agents_executed": {
                        "mysql": bool(result.get('mysql_analysis')) if result else False,
                        "shortage": bool(result.get('shortage_analysis')) if result else False,
                        "alert": bool(result.get('alert_management')) if result else False
                    }
                }
            )
            
            # Log results
            success = result.get('success', False)
            execution_time = result.get('execution_time', 0)
            workflow_id = result.get('workflow_id', 'unknown')
            
            logger.info(f"Result: {'✓ SUCCESS' if success else '✗ FAILED'}")
            logger.info(f"Execution Time: {execution_time:.2f}s")
            logger.info(f"Workflow ID: {workflow_id}")
            
            if success:
                query_analysis = result.get('query_analysis', {})
                logger.info(f"Query Type: {query_analysis.get('type', 'unknown')}")
                logger.info(f"Confidence: {query_analysis.get('confidence', 0):.2f}")
                logger.info(f"Workflow Pattern: {query_analysis.get('workflow_pattern', 'unknown')}")
                
                # Log agent results with detailed debug information
                if result.get('mysql_analysis'):
                    mysql_analysis = result['mysql_analysis']
                    mysql_success = mysql_analysis.get('success', False)
                    logger.info(f"  MySQL Analysis: {'✓' if mysql_success else '✗'}")
                    
                    # Debug log MySQL agent details
                    log_llm_interaction(
                        "MYSQL_AGENT_RESULT",
                        "MySQL Agent",
                        response=mysql_analysis.get('response', 'No response'),
                        extra_data={
                            "success": mysql_success,
                            "context": mysql_analysis.get('context', ''),
                            "scenario_num": scenario_num
                        }
                    )
                
                if result.get('shortage_analysis'):
                    shortage_analysis = result['shortage_analysis']
                    shortage_success = shortage_analysis.get('success', False)
                    shortage_risk = shortage_analysis.get('risk_level', 'unknown')
                    logger.info(f"  Shortage Analysis: {'✓' if shortage_success else '✗'} (Risk: {shortage_risk})")
                    
                    # Debug log Shortage agent details
                    log_llm_interaction(
                        "SHORTAGE_AGENT_RESULT",
                        "Shortage Agent",
                        response=shortage_analysis.get('response', 'No response'),
                        extra_data={
                            "success": shortage_success,
                            "shortage_index": shortage_analysis.get('shortage_index', 0),
                            "risk_level": shortage_risk,
                            "company_name": shortage_analysis.get('company_name', ''),
                            "scenario_num": scenario_num
                        }
                    )
                
                if result.get('alert_management'):
                    alert_management = result['alert_management']
                    alert_success = alert_management.get('success', False)
                    alerts_sent = len(alert_management.get('alerts_sent', []))
                    logger.info(f"  Alert Management: {'✓' if alert_success else '✗'} ({alerts_sent} alerts)")
                    
                    # Debug log Alert agent details
                    log_llm_interaction(
                        "ALERT_AGENT_RESULT",
                        "Alert Agent",
                        response=alert_management.get('alert_summary', 'No summary'),
                        extra_data={
                            "success": alert_success,
                            "alerts_sent_count": alerts_sent,
                            "alerts_sent": alert_management.get('alerts_sent', []),
                            "notification_results": alert_management.get('notification_results', []),
                            "scenario_num": scenario_num
                        }
                    )
            
            else:
                error = result.get('error', 'Unknown error')
                logger.error(f"Error: {error}")
                
                # Check for clarification requirements
                if result.get('requires_clarification', False):
                    logger.warning("Query requires clarification:")
                    for clarification in result.get('suggested_clarifications', []):
                        logger.warning(f"  - {clarification}")
            
            return result
            
        except Exception as e:
            logger.error(f"Demo scenario {scenario_num} failed with exception: {e}")
            return {"success": False, "error": str(e), "scenario": scenario['name']}
    
    def _display_demo_summary(self, results: list) -> None:
        """Display summary of all demo results."""
        logger.info(f"\n=== Demo Results Summary ===")
        successful_scenarios = sum(1 for r in results if r.get('success', False))
        total_scenarios = len(results)
        
        logger.info(f"Successful Scenarios: {successful_scenarios}/{total_scenarios}")
        
        # Execution statistics
        stats = self.orchestration_runner.get_execution_statistics()
        logger.info(f"Total Workflows Executed: {stats['total_workflows']}")
        logger.info(f"Average Execution Time: {stats['average_execution_time']:.2f}s")
    
    async def interactive_mode(self) -> None:
        """Run interactive query mode."""
        if not self.orchestration_runner:
            logger.error("Orchestration system not initialized")
            return
        
        logger.info("\n=== Interactive Orchestration Mode ===")
        logger.info("Enter financial analysis queries. Type 'quit' to exit.")
        logger.info("Example queries:")
        logger.info("  - Analyze shortage for order CUSTORD-202506001")
        logger.info("  - Check supplier risk for MetaMind Technology")
        logger.info("  - Review customer priorities for Tech Pioneer")
        
        while True:
            try:
                query = input("\nQuery> ").strip()
                
                if query.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not query:
                    continue
                
                logger.info(f"Executing query: {query}")
                
                # Debug log interactive query start
                log_llm_interaction(
                    "INTERACTIVE_QUERY_START",
                    "OrchestrationRunner",
                    query=query,
                    extra_data={
                        "execution_mode": "pattern_based",
                        "interaction_type": "interactive"
                    }
                )
                
                import time
                interactive_start_time = time.time()
                
                result = await self.orchestration_runner.execute_financial_query(
                    query=query,
                    execution_mode="pattern_based"
                )
                
                interactive_execution_time = time.time() - interactive_start_time
                
                # Debug log interactive query completion
                log_llm_interaction(
                    "INTERACTIVE_QUERY_COMPLETE",
                    "OrchestrationRunner",
                    response=str(result.get('final_response', 'No final response')) if result else 'No result',
                    execution_time=interactive_execution_time,
                    extra_data={
                        "success": result.get('success', False) if result else False,
                        "workflow_id": result.get('workflow_id', 'unknown') if result else 'unknown',
                        "interaction_type": "interactive"
                    }
                )
                
                # Display results
                success = result.get('success', False)
                print(f"\nResult: {'SUCCESS' if success else 'FAILED'}")
                
                if success:
                    execution_time = result.get('execution_time', 0)
                    print(f"Execution Time: {execution_time:.2f}s")
                    
                    query_analysis = result.get('query_analysis', {})
                    print(f"Query Type: {query_analysis.get('type', 'unknown')}")
                    print(f"Workflow Pattern: {query_analysis.get('workflow_pattern', 'unknown')}")
                    
                    # Display brief agent results
                    if result.get('mysql_analysis', {}).get('success'):
                        mysql_response = result['mysql_analysis'].get('response', 'No response')
                        print(f"\nMySQL Analysis: {mysql_response[:200]}{'...' if len(mysql_response) > 200 else ''}")
                    
                    if result.get('shortage_analysis', {}).get('success'):
                        shortage_index = result['shortage_analysis'].get('shortage_index', 0)
                        risk_level = result['shortage_analysis'].get('risk_level', 'unknown')
                        print(f"\nShortage Analysis: Index {shortage_index:.3f}, Risk {risk_level}")
                    
                    if result.get('alert_management', {}).get('success'):
                        alerts_count = len(result['alert_management'].get('alerts_sent', []))
                        print(f"\nAlert Management: {alerts_count} alerts processed")
                
                else:
                    error = result.get('error', 'Unknown error')
                    print(f"Error: {error}")
                    
                    if result.get('requires_clarification'):
                        print("\nQuery needs clarification:")
                        for clarification in result.get('suggested_clarifications', []):
                            print(f"  - {clarification}")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"Interactive query failed: {e}")
        
        logger.info("Interactive mode ended")


async def main():
    """Main entry point for orchestrated financial analysis."""
    global debug_log_file, llm_debug_file, logger

    # Parse command line arguments
    show_llm_realtime = True
    args = sys.argv[1:]

    # Check for --no-llm-realtime flag
    if '--no-llm-realtime' in args:
        show_llm_realtime = False
        args.remove('--no-llm-realtime')

    # Initialize logging with real-time LLM debug display setting
    debug_log_file, llm_debug_file = setup_comprehensive_logging(show_llm_realtime=show_llm_realtime)
    logger = logging.getLogger(__name__)

    demo = OrchestrationDemo()

    # Initialize system
    if not await demo.initialize_system():
        logger.error("Failed to initialize orchestration system")
        return False

    # Run health check
    await demo.run_health_check()

    # Check remaining command line arguments
    if len(args) > 0:
        mode = args[0].lower()

        if mode == "demo":
            # Check if specific demo scenario is requested
            if len(args) > 1:
                try:
                    scenario_num = int(args[1])
                    await demo.run_demo_scenarios(scenario_num)
                except ValueError:
                    logger.error(f"Invalid scenario number: {args[1]}. Must be a number between 1-4.")
                    print_usage()
                    return False
            else:
                # Run all demo scenarios
                await demo.run_demo_scenarios()

        elif mode == "interactive":
            await demo.interactive_mode()

        elif mode == "health":
            # Only health check (already done above)
            pass

        elif mode in ["help", "--help", "-h"]:
            print_usage()

        else:
            logger.error(f"Unknown mode: {mode}")
            print_usage()
            return False
    else:
        # Default: run all demo scenarios
        await demo.run_demo_scenarios()
    
    logger.info("\n=== Orchestrated Financial Analysis Complete ===")

    # Create comprehensive log summary report
    logger.info("Creating debug log summary report...")
    summary_file = create_log_summary_report()
    if summary_file:
        logger.info(f"Debug log summary report created: {summary_file}")
        logger.info(f"Main debug log: {debug_log_file}")
        logger.info(f"LLM reasoning log: {llm_debug_file}")

    return True


def print_usage():
    """Print usage instructions."""
    print("""
Usage: python orchestrated_main.py [mode] [scenario_number] [--no-llm-realtime]

Modes:
  demo [1-4]    Run demo scenarios (all scenarios if no number specified)
                1: Database Shortage Analysis Query
                2: Database Supplier Risk Query
                3: Database Customer Priority Query
                4: Database Comprehensive Analysis Query
  interactive   Enter interactive query mode
  health        Run system health check only
  help          Show this help message

Options:
  --no-llm-realtime    Disable real-time LLM reasoning display (quieter output)

Examples:
  python orchestrated_main.py                    # Run all demo scenarios with real-time LLM debug
  python orchestrated_main.py demo               # Run all demo scenarios with real-time LLM debug
  python orchestrated_main.py demo 1             # Run demo scenario 1 only with real-time LLM debug
  python orchestrated_main.py demo 3             # Run demo scenario 3 only with real-time LLM debug
  python orchestrated_main.py interactive        # Enter interactive mode with real-time LLM debug
  python orchestrated_main.py health             # Health check only
  python orchestrated_main.py demo 1 --no-llm-realtime  # Run without real-time LLM display
""")


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\nInterrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)