# WebSocket Streaming Migration Guide

## Overview

This guide documents the new streaming endpoints and functionality added to the MCP Financial Analyzer. Three new WebSocket endpoints have been integrated to provide real-time streaming capabilities for MySQL database analysis, storage/shortage analysis, and alert management.

## New WebSocket Endpoints

### 1. MySQL Database Analysis Endpoint

**URL**: `/ws/mysql/{user_id}?company=<company_name>`

**Purpose**: Real-time MySQL database analysis with streaming query execution and results

**Parameters**:
- `user_id`: Unique identifier for the WebSocket connection
- `company` (query parameter): Company name for context (default: "General")

**Features**:
- Real-time SQL query execution
- Streaming query results and analysis
- MCP tool integration for database operations
- Tool stream events for database calls

### 2. Storage/Shortage Analysis Endpoint

**URL**: `/ws/storage/{user_id}?company=<company_name>`

**Purpose**: Real-time supply chain and shortage analysis with streaming workflow updates

**Parameters**:
- `user_id`: Unique identifier for the WebSocket connection  
- `company` (query parameter): Company name for analysis context (default: "General")

**Features**:
- Streaming shortage index calculations
- Real-time MCP tool integration with shortage-index server
- Component-by-component analysis updates
- Risk assessment with streaming recommendations

### 3. Alert Management Endpoint

**URL**: `/ws/alert/{user_id}?company=<company_name>`

**Purpose**: Real-time alert evaluation, prioritization, and notification delivery

**Parameters**:
- `user_id`: Unique identifier for the WebSocket connection
- `company` (query parameter): Company name for alert context (default: "General")

**Features**:
- Streaming alert condition evaluation
- Real-time notification delivery status
- Multi-channel notification progress (Email, MQTT, HTTP)
- Alert lifecycle tracking with updates

## Message Flow Examples

### MySQL Database Analysis Flow

```javascript
// Client sends query
{
    "message": "Show me current inventory levels for critical components",
    "streaming": true
}

// Server streaming response sequence:
{
    "type": "stream_start",
    "message": "🔍 Analyzing MySQL query...",
    "user_id": "user333"
}

{
    "type": "stream_chunk", 
    "message": "**Orchestrator Reasoning:** To determine inventory levels...",
    "user_id": "user333"
}

{
    "type": "tool_stream",
    "data": {
        "type": "tool_call_start",
        "tool_name": "mysql_agent",
        "args": {"query": "SELECT * FROM inventory WHERE category='critical'"},
        "timestamp": "2024-01-15T10:30:45.123Z"
    },
    "user_id": "user333"
}

{
    "type": "stream_chunk",
    "message": "🔧 **Executing tool:** mysql_agent\n**Parameters:** {\"query\": \"SELECT * FROM inventory WHERE category='critical'\"}\n",
    "user_id": "user333"
}

{
    "type": "tool_stream",
    "data": {
        "type": "tool_call_result", 
        "tool_name": "mysql_agent",
        "result": {"content": ["Critical components: CPU (45 units), GPU (23 units), Memory (67 units)"], "is_error": false},
        "timestamp": "2024-01-15T10:30:47.456Z"
    },
    "user_id": "user333"
}

{
    "type": "stream_chunk",
    "message": "**Result:** Critical components: CPU (45 units), GPU (23 units), Memory (67 units)\n",
    "user_id": "user333"
}

{
    "type": "stream_end",
    "message": "✅ **Final Response:**\nInventory analysis complete. Critical components show adequate stock levels with CPU at 45 units, GPU at 23 units, and Memory at 67 units.",
    "full_response": "Inventory analysis complete...",
    "user_id": "user333"
}
```

### Storage/Shortage Analysis Flow

```javascript
// Client sends shortage analysis request
{
    "message": "Analyze component shortages for Q4 production requirements",
    "streaming": true
}

// Server streaming response sequence:
{
    "type": "stream_start",
    "message": "🔍 Initiating shortage analysis...",
    "user_id": "user444"
}

{
    "type": "stream_chunk",
    "message": "📊 Processing analysis request...",
    "user_id": "user444"
}

{
    "type": "tool_stream", 
    "data": {
        "type": "tool_call_start",
        "tool_name": "ShortageIndex",
        "args": {"required_qty": [250, 180, 400], "available_qty": [180, 45, 200]},
        "timestamp": "2024-01-15T10:35:12.789Z"
    },
    "user_id": "user444"
}

{
    "type": "tool_stream",
    "data": {
        "type": "tool_call_result",
        "tool_name": "ShortageIndex", 
        "result": {"content": ["0.833"], "is_error": false},
        "timestamp": "2024-01-15T10:35:15.234Z"
    },
    "user_id": "user444"
}

{
    "type": "tool_stream",
    "data": {
        "type": "tool_call_start",
        "tool_name": "WeightedShortageIndex",
        "args": {"required_qty": [250, 180, 400], "available_qty": [180, 45, 200], "weights": [0.3, 0.4, 0.3]},
        "timestamp": "2024-01-15T10:35:16.123Z"
    },
    "user_id": "user444"
}

{
    "type": "tool_stream", 
    "data": {
        "type": "tool_call_result",
        "tool_name": "WeightedShortageIndex",
        "result": {"content": ["0.626"], "is_error": false},
        "timestamp": "2024-01-15T10:35:18.567Z"
    },
    "user_id": "user444"
}

{
    "type": "stream_chunk",
    "message": "✅ Analysis complete!\n\n**Shortage Index:** 0.833\n**Risk Level:** HIGH\n",
    "user_id": "user444"
}

{
    "type": "stream_end",
    "message": "Shortage Analysis Report for MetaMind Technology Co.\n========================================\n\nOverall Shortage Index: 0.833\nRisk Level: HIGH\nWeighted Shortage Index: 0.626\n\nAnalysis:\nShortage Index Analysis: 0.833\nWeighted Shortage Index: 0.626\nComponent Analysis:\n- primary_processors: 180.0/250.0 available (shortage: 28.0%, weight: 30.0%)\n- graphics_processors: 45.0/180.0 available (shortage: 75.0%, weight: 40.0%)\n- memory_modules: 200.0/400.0 available (shortage: 50.0%, weight: 30.0%)\n\nRecommendations:\n1. URGENT: Immediate action required to address critical shortages\n2. Activate emergency procurement procedures\n3. Consider alternative suppliers or substitute materials",
    "full_response": "Shortage Analysis Report...",
    "user_id": "user444"
}
```

### Alert Management Flow

```javascript
// Client sends alert evaluation request
{
    "message": "Evaluate critical alerts and send notifications for supply chain disruptions",
    "streaming": true
}

// Server streaming response sequence:
{
    "type": "stream_start",
    "message": "🚨 Initiating alert management analysis...",
    "user_id": "user555"
}

{
    "type": "stream_chunk",
    "message": "📊 Processing alert conditions...",
    "user_id": "user555"  
}

{
    "type": "tool_stream",
    "data": {
        "type": "tool_call_start",
        "tool_name": "EmailNotification",
        "args": {"recipient": "<EMAIL>", "alert_id": "ALERT_001"},
        "timestamp": "2024-01-15T10:40:23.456Z"
    },
    "user_id": "user555"
}

{
    "type": "tool_stream",
    "data": {
        "type": "tool_call_result", 
        "tool_name": "EmailNotification",
        "result": {"content": ["Email notification blocked (demo mode)"], "is_error": false},
        "timestamp": "2024-01-15T10:40:24.789Z"
    },
    "user_id": "user555"
}

{
    "type": "tool_stream",
    "data": {
        "type": "tool_call_start",
        "tool_name": "MqttNotification",
        "args": {"topic": "alerts/supply_chain", "alert_id": "ALERT_002"},
        "timestamp": "2024-01-15T10:40:25.123Z"
    },
    "user_id": "user555"
}

{
    "type": "tool_stream",
    "data": {
        "type": "tool_call_result",
        "tool_name": "MqttNotification", 
        "result": {"content": ["MQTT notification sent successfully"], "is_error": false},
        "timestamp": "2024-01-15T10:40:26.456Z"
    },
    "user_id": "user555"
}

{
    "type": "stream_chunk",
    "message": "✅ Alert analysis complete!\n\n**Alerts Generated:** 2\n  1. supply_chain: critical priority\n  2. financial: high priority\n\n**Notifications Sent:** 2\n",
    "user_id": "user555"
}

{
    "type": "stream_end",
    "message": "Alert Management Analysis for Supply Chain Inc.\n==============================================\n\n2 critical alerts generated for supply chain disruptions.\nNotifications sent via MQTT and Email channels.\nImmediate action recommended for critical severity alerts.",
    "full_response": "Alert Management Analysis...",
    "user_id": "user555"
}
```

## Agent-Specific Streaming Features

### MySQL Database Analysis

**Unique Streaming Capabilities**:
- **SQL Query Execution Progress**: Real-time updates during query execution
- **Result Streaming**: Large result sets streamed incrementally
- **Database Connection Status**: Connection health and performance metrics
- **Query Optimization Feedback**: Suggestions for query improvements

**Tool Stream Events**:
- `mysql_agent`: SQL query execution with parameters and results
- Tool calls include query text, execution time, and result metadata

### Storage/Shortage Analysis

**Unique Streaming Capabilities**:
- **Shortage Analysis Workflow Phases**: Data extraction, MCP tool calls, risk analysis, recommendations
- **Component-by-Component Updates**: Individual component shortage calculations
- **Risk Assessment Progress**: Real-time risk level determination
- **MCP Tool Integration**: ShortageIndex and WeightedShortageIndex tool calls

**Tool Stream Events**:
- `ShortageIndex`: Basic shortage index calculations
- `WeightedShortageIndex`: Strategic component weighting analysis  
- Tool calls include required/available quantities, weights, and calculated indices

### Alert Management

**Unique Streaming Capabilities**:
- **Alert Evaluation Progress**: Threshold checking and condition evaluation
- **Notification Delivery Status**: Multi-channel delivery progress tracking
- **Alert Prioritization**: Real-time severity and urgency calculation
- **Channel Health Monitoring**: Notification channel availability and performance

**Tool Stream Events**:
- `EmailNotification`: Email alert delivery
- `MqttNotification`: MQTT message publishing
- `HttpNotification`: HTTP webhook delivery
- Tool calls include recipient details, message content, and delivery status

## Integration Examples

### JavaScript WebSocket Client

```javascript
// MySQL Database Analysis Client
const mysqlWs = new WebSocket('ws://localhost:8000/ws/mysql/user123?company=TechCorp');

mysqlWs.onopen = function() {
    console.log('Connected to MySQL analysis endpoint');
};

mysqlWs.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    switch(data.type) {
        case 'stream_start':
            console.log('Analysis started:', data.message);
            break;
            
        case 'stream_chunk':
            console.log('Progress update:', data.message);
            break;
            
        case 'tool_stream':
            console.log('Tool event:', data.data.type, data.data.tool_name);
            if (data.data.type === 'tool_call_result') {
                console.log('Tool result:', data.data.result);
            }
            break;
            
        case 'stream_end':
            console.log('Analysis complete:', data.full_response);
            break;
            
        case 'error':
            console.error('Error:', data.message);
            break;
    }
};

// Send analysis request
mysqlWs.send(JSON.stringify({
    message: "Analyze inventory turnover rates by product category",
    streaming: true
}));
```

### Python WebSocket Client

```python
import asyncio
import websockets
import json

async def storage_analysis_client():
    uri = "ws://localhost:8000/ws/storage/user456?company=ManufacturingCorp"
    
    async with websockets.connect(uri) as websocket:
        print("Connected to Storage Analysis endpoint")
        
        # Send analysis request
        request = {
            "message": "Analyze Q4 component shortages with weighted priorities",
            "streaming": True
        }
        await websocket.send(json.dumps(request))
        
        # Process streaming responses
        async for message in websocket:
            data = json.loads(message)
            
            if data['type'] == 'stream_start':
                print(f"Analysis started: {data['message']}")
            elif data['type'] == 'stream_chunk':
                print(f"Update: {data['message']}")
            elif data['type'] == 'tool_stream':
                tool_event = data['data']
                print(f"Tool {tool_event['type']}: {tool_event['tool_name']}")
                if tool_event['type'] == 'tool_call_result':
                    print(f"Result: {tool_event['result']['content']}")
            elif data['type'] == 'stream_end':
                print(f"Analysis complete!\n{data['full_response']}")
                break
            elif data['type'] == 'error':
                print(f"Error: {data['message']}")
                break

# Run the client
asyncio.run(storage_analysis_client())
```

## Migration Notes

### Existing Non-Streaming Usage

**Important**: All existing non-streaming usage of these agents remains unchanged. The agents maintain backward compatibility:

- **MySQL Agent**: `create_mysql_orchestrator_agent()` works as before
- **Storage Analyzer**: `create_shortage_analyzer_agent()` maintains existing API  
- **Alert Manager**: `create_alert_manager_agent()` and `create_enhanced_alert_manager_agent()` unchanged

### Opting Into Streaming Functionality

To enable streaming capabilities, agents must be created with the `tool_stream_callback` parameter:

```python
# Enable streaming for MySQL agent
mysql_agent = create_mysql_orchestrator_agent(
    tool_stream_callback=my_stream_callback
)

# Enable streaming for Storage Analyzer
storage_agent = create_shortage_analyzer_agent(
    company_name="MyCompany",
    tool_stream_callback=my_stream_callback  
)

# Enable streaming for Alert Manager
alert_agent = create_alert_manager_agent(
    company_name="MyCompany", 
    alert_config=my_config,
    tool_stream_callback=my_stream_callback
)
```

### Session Management Updates

The `SessionType` enum has been extended with three new values:
- `SessionType.MYSQL = "mysql"`
- `SessionType.STORAGE_ANALYZER = "storage_analyzer"`  
- `SessionType.ALERT_MANAGER = "alert_manager"`

Session initialization automatically configures streaming wrappers when these session types are used.

## Troubleshooting

### Common Issues and Solutions

#### Connection Issues

**Problem**: WebSocket connection fails or drops
**Solution**: 
- Verify the server is running on port 8000
- Check that the endpoint URLs are correct
- Ensure company parameter is properly URL-encoded

#### Missing Tool Stream Events

**Problem**: Not receiving `tool_stream` messages
**Solution**:
- Verify agents are created with `tool_stream_callback` parameter
- Check that MCP servers (mysql, shortage-index, alert-notification) are running
- Review server logs for MCP connection errors

#### Streaming Messages Not Received

**Problem**: Only getting `stream_end` without intermediate chunks
**Solution**:
- Ensure `streaming: true` is set in client messages
- Check that `StreamingVLLMLLM` wrapper is properly initialized  
- Verify VLLM server is accessible and responding

#### Agent Initialization Failures

**Problem**: Agents fail to initialize with streaming capabilities
**Solution**:
- Verify all required dependencies are installed
- Check MCP server configuration in `mcp_agent.config.yaml`
- Ensure VLLM server is running and accessible

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

Check the following log files:
- `logs/mcp-agent-{timestamp}.jsonl`: Agent execution logs
- `logs/main_debug.log`: WebSocket server logs

### Health Check Endpoints

Use these endpoints to verify system health:

- **General Health**: `GET http://localhost:8000/health`
- **Session Status**: `GET http://localhost:8000/sessions`

The health check now includes the new endpoints in the response:
```json
{
    "status": "healthy",
    "active_sessions": 3,
    "endpoints": ["research", "analyze", "report", "full_analysis", "orchestration", "mysql", "storage", "alert"],
    "timestamp": "2024-01-15T10:45:30.123Z"
}
```

## Performance Considerations

### Concurrent Connections

Each WebSocket endpoint supports multiple concurrent connections. Session management automatically handles:
- Connection pooling and cleanup
- Memory management for active sessions
- Automatic session timeout (2 hours of inactivity)

### Streaming Optimization

For optimal streaming performance:
- Use appropriate chunk sizes (default: 3-5 words per chunk)
- Configure streaming delays based on network conditions
- Monitor memory usage for long-running sessions

### MCP Tool Performance

Tool stream events add minimal overhead:
- Events are asynchronous and non-blocking
- Failed event delivery doesn't affect tool execution
- Events are automatically batched for efficiency

## Security Considerations

### Authentication

Currently, WebSocket endpoints use user_id for session identification. In production:
- Implement proper authentication tokens
- Add authorization checks for company data access
- Use WSS (WebSocket Secure) for encrypted connections

### Data Privacy

Streaming messages may contain sensitive financial data:
- Ensure proper access controls are in place
- Log access for audit trails  
- Consider data masking for non-production environments

### Rate Limiting

Implement rate limiting to prevent abuse:
- Limit concurrent connections per user
- Throttle message frequency
- Set maximum session duration

## Future Enhancements

### Planned Features

1. **WebSocket Authentication**: JWT token-based authentication
2. **Message Persistence**: Option to persist streaming messages
3. **Connection Recovery**: Automatic reconnection with state recovery
4. **Binary Streaming**: Support for large file transfers
5. **Compression**: Message compression for improved performance

### Extension Points

The streaming architecture is designed for extensibility:
- New session types can be added to `SessionType` enum
- Additional agents can implement streaming interfaces
- Custom tool stream events can be defined
- Message formats can be extended without breaking compatibility

---

For additional support or questions about the streaming functionality, refer to the test suite in `tests/test_streaming_agents.py` or contact the development team.