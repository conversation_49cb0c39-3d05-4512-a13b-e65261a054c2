# Orchestration Streaming Test Guide

This guide provides comprehensive testing tools to verify that the orchestrated shortage workflow delivers real-time streaming for both LLM text chunks and MCP tool call events.

## Overview

The orchestrated shortage workflow implements a MySQL → Shortage Analysis → Alert Management pipeline with real-time streaming capabilities. These tests validate:

1. **Real-time LLM text streaming**: Text chunks appear as generated, not in batches
2. **Real-time MCP tool call event streaming**: Tool events stream immediately as they occur  
3. **Orchestrated workflow verification**: Complete pipeline works with streaming
4. **Performance comparison**: Streaming matches individual MySQL agent performance
5. **Workflow stage progression**: Stages transition in real-time with proper tracking

## Test Files

### Core Test Scripts

- **`test_orchestration_streaming_validation.py`**: Comprehensive validation of orchestration streaming
- **`test_streaming_performance_comparison.py`**: Performance comparison between orchestration and MySQL agent
- **`run_orchestration_streaming_tests.py`**: Complete test suite runner
- **`verify_server_setup.py`**: Server connectivity and endpoint verification

### Supporting Files

- **`ORCHESTRATION_STREAMING_TEST_GUIDE.md`**: This guide
- **Test result files**: JSON files with detailed test results and metrics

## Prerequisites

### 1. Server Setup

Ensure the FastAPI WebSocket server is running:

```bash
cd mcp-agent/examples/usecases/mcp_financial_analyzer/fastapi_websocket
python main.py
```

The server should be accessible at `ws://localhost:8000` by default.

### 2. MCP Services

Ensure required MCP services are running:

- **MySQL MCP Server**: Port 6970
- **Shortage Analysis MCP Server**: Port 6971  
- **Alert Management MCP Server**: Port 6972

### 3. Dependencies

Install required Python packages:

```bash
pip install websockets asyncio
```

## Running the Tests

### Quick Verification

First, verify the server setup:

```bash
python verify_server_setup.py
```

This checks:
- Server connectivity
- Endpoint accessibility
- Basic response functionality

Expected output:
```
✅ SERVER SETUP VERIFICATION
MySQL Agent: ✅ WORKING - Response time: 1.23s
Orchestration (Pattern-based): ✅ WORKING - Response time: 2.45s
Overall Status: ✅ READY
```

### Complete Test Suite

Run the comprehensive test suite:

```bash
python run_orchestration_streaming_tests.py
```

This executes all tests:
1. Orchestration streaming validation
2. Performance comparison with MySQL agent
3. Workflow stage verification
4. Real-time latency analysis

Expected output:
```
🧪 ORCHESTRATION STREAMING TEST SUITE
Test 1: Orchestration Streaming Validation - ✅ PASS
Test 2: Streaming Performance Comparison - ✅ PASS  
Test 3: Workflow Stage Verification - ✅ PASS
Test 4: Real-time Latency Analysis - ✅ PASS
Overall Result: ✅ PASS
```

### Individual Tests

Run specific tests individually:

#### Orchestration Streaming Validation
```bash
python test_orchestration_streaming_validation.py
```

Validates:
- Time to first chunk ≤ 5 seconds
- Minimum 10 text chunks received
- Minimum 3 tool events received
- Workflow stages detected
- No errors during streaming

#### Performance Comparison
```bash
python test_streaming_performance_comparison.py
```

Compares:
- Time to first chunk (orchestration vs MySQL)
- Chunk throughput rates
- Tool event frequencies
- Overall streaming performance

## Test Scenarios

### Default Test Message

The tests use this message to trigger the full pipeline:

```
CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION.
```

This message triggers:
1. **MySQL Analysis**: Database query for component requirements
2. **Shortage Analysis**: Calculate shortage indices for components
3. **Alert Management**: Generate customer notifications

### Expected Workflow Stages

The tests detect these workflow stages in the streaming output:

1. **stream_start**: Initial streaming setup
2. **mysql_analysis**: Database analysis phase
3. **shortage_analysis**: Shortage calculation phase  
4. **alert_management**: Alert generation phase
5. **stream_end**: Streaming completion

## Performance Criteria

### Streaming Performance

- **Time to First Chunk**: ≤ 5 seconds
- **Minimum Text Chunks**: ≥ 10 chunks
- **Minimum Tool Events**: ≥ 3 events
- **Inter-chunk Latency**: ≤ 2 seconds average
- **Maximum Latency**: ≤ 5 seconds between chunks

### Comparison Criteria

- **Time to First Chunk Ratio**: Orchestration ≤ 2x MySQL agent
- **Chunk Throughput Ratio**: Orchestration ≥ 50% of MySQL agent
- **Tool Events**: Orchestration ≥ 3 events (more than MySQL)
- **Error Rate**: 0% for both endpoints

## Understanding Test Results

### Success Indicators

✅ **PASS Results**:
- All endpoints accessible and responsive
- Streaming starts within acceptable time
- Text chunks stream in real-time
- Tool events stream immediately
- Workflow stages progress correctly
- Performance comparable to MySQL agent

### Failure Indicators

❌ **FAIL Results**:
- Server connectivity issues
- Slow time to first chunk (> 5s)
- Insufficient streaming events
- Missing workflow stages
- Poor performance vs MySQL agent
- Errors during streaming

### Partial Success

⚠️ **PARTIAL Results**:
- Some endpoints working, others failing
- Streaming works but performance below expectations
- Workflow stages detected but incomplete
- Tool events present but fewer than expected

## Troubleshooting

### Common Issues

#### Server Not Running
```
❌ Connection error: [Errno 111] Connection refused
```
**Solution**: Start the FastAPI server with `python main.py`

#### MCP Services Down
```
❌ Tool events: 0 (expected ≥ 3)
```
**Solution**: Verify MCP servers are running on ports 6970, 6971, 6972

#### Slow Streaming
```
❌ Time to first chunk: 8.45s (expected ≤ 5s)
```
**Solution**: Check server load, network latency, or LLM response times

#### Missing Workflow Stages
```
❌ Workflow stages detected: 1 (expected ≥ 2)
```
**Solution**: Verify orchestration runner is properly initialized

### Debug Mode

Run tests with detailed logging:

```bash
export PYTHONPATH=.
python -u test_orchestration_streaming_validation.py 2>&1 | tee test_debug.log
```

### Server Logs

Check server logs for errors:

```bash
tail -f logs/main_debug.log
```

## Test Result Files

Tests generate detailed JSON result files:

- **`orchestration_streaming_validation_YYYYMMDD_HHMMSS.json`**: Validation results
- **`streaming_performance_comparison_YYYYMMDD_HHMMSS.json`**: Performance comparison
- **`orchestration_streaming_test_suite_YYYYMMDD_HHMMSS.json`**: Complete suite results
- **`server_verification_YYYYMMDD_HHMMSS.json`**: Server verification results

These files contain:
- Detailed timing metrics
- All streaming events captured
- Tool call event data
- Error logs and diagnostics
- Performance analysis

## Integration with CI/CD

### Automated Testing

Add to CI pipeline:

```bash
# Verify server setup
python verify_server_setup.py || exit 1

# Run comprehensive tests
python run_orchestration_streaming_tests.py || exit 1
```

### Performance Monitoring

Use test results for performance monitoring:

```bash
# Extract key metrics
python -c "
import json
with open('orchestration_streaming_test_suite_*.json') as f:
    results = json.load(f)
    print(f'TTFC: {results[\"tests\"][\"orchestration_validation\"][\"streaming_metrics\"][\"time_to_first_chunk_seconds\"]}s')
    print(f'Chunks: {results[\"tests\"][\"orchestration_validation\"][\"streaming_metrics\"][\"total_chunks\"]}')
    print(f'Tools: {results[\"tests\"][\"orchestration_validation\"][\"streaming_metrics\"][\"total_tool_events\"]}')
"
```

## Expected Behavior

### Successful Test Run

A successful test run demonstrates:

1. **WebSocket Connection**: Persistent connection throughout workflow
2. **Real-time Text Streaming**: Chunks appear immediately as generated
3. **Real-time Tool Events**: Tool calls stream with proper event types
4. **Workflow Progression**: Clear stage transitions (MySQL → Shortage → Alert)
5. **Performance Parity**: Comparable to individual MySQL agent performance
6. **Error-free Execution**: No connection drops or processing errors

### Streaming Event Sequence

Expected event sequence:
```
1. stream_start
2. stream_chunk (multiple, real-time)
3. mcp_tool_stream (MySQL tools)
4. stream_chunk (analysis progress)
5. mcp_tool_stream (Shortage tools)
6. stream_chunk (shortage results)
7. mcp_tool_stream (Alert tools)
8. stream_chunk (final results)
9. stream_end
```

This validates that the orchestrated shortage workflow delivers true real-time streaming capabilities matching the performance of individual agent endpoints.
