{"server_url": "ws://localhost:8000", "test_time": "2025-09-18T11:45:17.664972", "endpoints": {"MySQL Agent": {"url": "ws://localhost:8000/ws/mysql/test_user", "accessible": false, "response_received": false, "error": "Multiple exceptions: [Errno 111] Connect call failed ('127.0.0.1', 8000), [Errno 99] Cannot assign requested address", "response_time_seconds": null}, "Orchestration (Pattern-based)": {"url": "ws://localhost:8000/ws/orchestration/test_user?mode=pattern_based", "accessible": false, "response_received": false, "error": "Multiple exceptions: [Errno 111] Connect call failed ('127.0.0.1', 8000), [Errno 99] Cannot assign requested address", "response_time_seconds": null}, "Orchestration (Orchestrator-based)": {"url": "ws://localhost:8000/ws/orchestration/test_user?mode=orchestrator_based", "accessible": false, "response_received": false, "error": "Multiple exceptions: [Errno 111] Connect call failed ('127.0.0.1', 8000), [Errno 99] Cannot assign requested address", "response_time_seconds": null}, "Storage Analyzer": {"url": "ws://localhost:8000/ws/storage/test_user", "accessible": false, "response_received": false, "error": "Multiple exceptions: [Errno 111] Connect call failed ('127.0.0.1', 8000), [Errno 99] Cannot assign requested address", "response_time_seconds": null}, "Alert Manager": {"url": "ws://localhost:8000/ws/alert/test_user", "accessible": false, "response_received": false, "error": "Multiple exceptions: [Errno 111] Connect call failed ('127.0.0.1', 8000), [Errno 99] Cannot assign requested address", "response_time_seconds": null}}, "overall_status": false, "summary": {"total_endpoints": 5, "accessible_endpoints": 0, "responsive_endpoints": 0, "accessibility_rate": 0.0, "responsiveness_rate": 0.0}}