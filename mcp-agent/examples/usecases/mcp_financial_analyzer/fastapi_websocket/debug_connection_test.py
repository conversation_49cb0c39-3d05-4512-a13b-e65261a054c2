#!/usr/bin/env python3
"""
Simple test to debug WebSocket connection issues
"""

import asyncio
import json
import websockets

async def test_mysql_connection():
    """Test MySQL endpoint connection"""
    url = "ws://localhost:8000/ws/mysql/debug_test_user?query=SHOW TABLES"
    print(f"Testing MySQL connection to: {url}")
    
    try:
        websocket = await websockets.connect(url)
        print("✅ MySQL connection successful!")
        
        # Send a test message
        test_message = {
            "message": "Show me all tables in the database",
            "streaming": True
        }
        await websocket.send(json.dumps(test_message))
        print("✅ Message sent successfully")
        
        # Wait for response
        response = await websocket.recv()
        print(f"✅ Response received: {response[:100]}...")
        
        await websocket.close()
        return True
        
    except Exception as e:
        print(f"❌ MySQL connection failed: {e}")
        return False

async def test_alert_connection():
    """Test Alert endpoint connection"""
    url = "ws://localhost:8000/ws/alert/debug_test_user?company=TestCompany"
    print(f"Testing Alert connection to: {url}")
    
    try:
        websocket = await websockets.connect(url)
        print("✅ Alert connection successful!")
        
        # Send a test message
        test_message = {
            "message": "Test critical alert processing",
            "streaming": True
        }
        await websocket.send(json.dumps(test_message))
        print("✅ Message sent successfully")
        
        # Wait for response
        response = await websocket.recv()
        print(f"✅ Response received: {response[:100]}...")
        
        await websocket.close()
        return True
        
    except Exception as e:
        print(f"❌ Alert connection failed: {e}")
        return False

async def test_storage_connection():
    """Test Storage endpoint connection (should work)"""
    url = "ws://localhost:8000/ws/storage/debug_test_user?company=TestCompany"
    print(f"Testing Storage connection to: {url}")
    
    try:
        websocket = await websockets.connect(url)
        print("✅ Storage connection successful!")
        
        # Send a test message
        test_message = {
            "message": "Test shortage analysis for CPU components",
            "streaming": True
        }
        await websocket.send(json.dumps(test_message))
        print("✅ Message sent successfully")
        
        # Wait for response
        response = await websocket.recv()
        print(f"✅ Response received: {response[:100]}...")
        
        await websocket.close()
        return True
        
    except Exception as e:
        print(f"❌ Storage connection failed: {e}")
        return False

async def main():
    print("🔍 Testing WebSocket connections...")
    print("=" * 50)
    
    mysql_ok = await test_mysql_connection()
    print()
    
    alert_ok = await test_alert_connection()
    print()
    
    storage_ok = await test_storage_connection()
    print()
    
    print("=" * 50)
    print("SUMMARY:")
    print(f"MySQL: {'✅ OK' if mysql_ok else '❌ FAIL'}")
    print(f"Alert: {'✅ OK' if alert_ok else '❌ FAIL'}")
    print(f"Storage: {'✅ OK' if storage_ok else '❌ FAIL'}")

if __name__ == "__main__":
    asyncio.run(main())