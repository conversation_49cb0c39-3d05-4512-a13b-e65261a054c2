# Agent Streaming Validation Test Suite

This comprehensive test suite validates **tool call streaming** and **LLM streaming** functionality for the three specialized agents in the MCP Financial Analyzer system through WebSocket connections.

## 🎯 What It Tests

### Agents Under Test
- **MySQL Agent** (`/ws/mysql/{user_id}`) - Database analysis operations
- **Storage Analyzer Agent** (`/ws/storage/{user_id}`) - Component shortage analysis  
- **Alert Manager Agent** (`/ws/alert/{user_id}`) - Alert processing and notifications

### Streaming Validation Points

#### 🔧 Tool Call Streaming
- ✅ **Tool Start Messages** - Tools begin execution with proper notifications
- ✅ **Tool Progress Updates** - Progress messages during long-running operations
- ✅ **Tool Result Streaming** - Results returned through streaming interface
- ✅ **Error Handling** - Graceful failure when tools encounter issues

#### 💬 LLM Response Streaming  
- ✅ **Stream Start/End** - Proper stream lifecycle management
- ✅ **Chunk Coherence** - Response chunks maintain logical flow
- ✅ **Message Ordering** - Tool calls complete before LLM processing
- ✅ **Performance** - Streaming latency within acceptable thresholds

#### 🚀 Advanced Scenarios
- ✅ **Concurrent Streaming** - Multiple agents streaming simultaneously
- ✅ **Error Recovery** - Connection drops and tool failures handled gracefully
- ✅ **Performance Benchmarks** - Latency and throughput validation

## 🏗️ Architecture

```
test_agent_streaming_validation.py    # Main test suite
├── TestMySQLAgentStreaming           # MySQL-specific streaming tests
├── TestStorageAnalyzerStreaming      # Storage analyzer streaming tests  
├── TestAlertManagerStreaming         # Alert manager streaming tests
├── TestConcurrentAndErrorHandling    # Concurrent & error scenarios
└── TestPerformanceBenchmarks         # Performance validation

streaming_test_client.py               # WebSocket test harness
├── StreamingWebSocketClient          # WebSocket connection management
├── AgentStreamingValidator           # High-level validation logic
├── StreamingMessage                  # Message parsing and validation
└── StreamingValidationResult         # Test result aggregation

test_scenarios.json                   # Test data and scenarios
├── mysql_agent_scenarios             # MySQL test cases
├── storage_analyzer_scenarios        # Storage test cases
├── alert_manager_scenarios           # Alert test cases
├── concurrent_test_scenarios         # Concurrent execution tests
└── performance_benchmarks            # Performance thresholds
```

## 🚀 Usage

### Prerequisites
1. **Start the WebSocket Server**:
   ```bash
   cd examples/usecases/mcp_financial_analyzer/fastapi_websocket
   python main.py
   ```

2. **Ensure Dependencies**:
   ```bash
   pip install websockets asyncio
   ```

### Running Tests

#### Option 1: Quick Test Runner (Recommended)
```bash
python run_streaming_tests.py
```

#### Option 2: Direct Test Execution
```bash
python test_agent_streaming_validation.py
```

#### Option 3: Individual Agent Testing
```python
# Test specific agent streaming
import asyncio
from streaming_test_client import test_endpoint_streaming

async def test_mysql():
    result = await test_endpoint_streaming(
        endpoint="/ws/mysql",
        user_id="test_user",
        test_message="SHOW TABLES and analyze the schema",
        connection_params={"query": "SHOW TABLES"},
        expected_tools=2,
        expected_chunks=5
    )
    print(f"Success: {result['success']}")

asyncio.run(test_mysql())
```

## 📊 Test Scenarios

### MySQL Agent Tests
- **Basic Table Query**: `SHOW TABLES` with schema analysis
- **Data Analysis**: `SELECT COUNT(*)` with insights generation
- **Performance Analysis**: `EXPLAIN SELECT` with optimization suggestions

### Storage Analyzer Tests  
- **Basic Shortage Analysis**: CPU shortage with ShortageIndex/WeightedShortageIndex tools
- **Complex Multi-Component**: GPU+RAM+Storage shortage analysis
- **Supply Chain Stress**: Critical shortage scenario with mitigation planning

### Alert Manager Tests
- **Critical System Alert**: Database failure with immediate notifications
- **Multi-Channel Notification**: Security alerts via email, SMS, Slack
- **Alert Aggregation**: Multiple related alerts with incident workflow

## 📈 Performance Benchmarks

### Latency Thresholds
```json
{
  "mysql_agent": {
    "max_first_response": 3.0,
    "max_chunk_latency": 1.5, 
    "max_total_duration": 30.0
  },
  "storage_analyzer": {
    "max_first_response": 5.0,
    "max_chunk_latency": 2.5,
    "max_total_duration": 50.0
  },
  "alert_manager": {
    "max_first_response": 2.0,
    "max_chunk_latency": 1.5,
    "max_total_duration": 25.0
  }
}
```

### Success Criteria
- ✅ **95%+ Success Rate** across all tests
- ✅ **Message Ordering** - Tools complete before LLM responses  
- ✅ **Stream Completeness** - Proper start/end message pairs
- ✅ **Error Handling** - Graceful degradation on failures
- ✅ **Concurrent Performance** - Multiple agents work simultaneously

## 📋 Test Report

Tests generate comprehensive JSON reports:

```json
{
  "test_execution_time": "2025-01-15T10:30:00",
  "total_tests": 15,
  "successful_tests": 14,
  "success_rate": 0.93,
  "agent_performance": {
    "mysql": {"test_count": 3, "average_duration": 12.5, "success_rate": 1.0},
    "storage": {"test_count": 3, "average_duration": 25.8, "success_rate": 0.89},
    "alert": {"test_count": 3, "average_duration": 8.2, "success_rate": 1.0}
  },
  "overall_performance": {
    "average_duration": 15.5,
    "average_latency": 0.85,
    "min_duration": 5.2,
    "max_duration": 45.1
  }
}
```

## 🐛 Troubleshooting

### Common Issues

**Connection Refused**:
```bash
# Ensure server is running
python main.py

# Check server health
curl http://localhost:8000/health
```

**Test Timeouts**:
```bash
# Check MCP server status in main.py logs
# Verify VLLM model is loaded properly
```

**Tool Call Failures**:
```bash  
# For storage analyzer - ensure SSE server is running
python scripts/start_shortage_server.py

# For MySQL - verify database connection in config
```

**Performance Issues**:
- Reduce `expected_chunks` in test scenarios
- Increase timeout thresholds in `test_scenarios.json`
- Check VLLM model performance

### Debug Mode
```bash
# Enable detailed logging
export PYTHONPATH=. 
python -m logging test_agent_streaming_validation.py --log-level DEBUG
```

## 🔧 Customization

### Adding New Test Scenarios
1. Edit `test_scenarios.json`
2. Add new scenario under appropriate agent section
3. Define expected tool calls and validation criteria

### Custom Validation Logic
1. Extend `BaseAgentStreamingTest` class
2. Override `validate_performance_metrics()` for custom thresholds
3. Add new test methods following naming convention `test_*_streaming()`

### Integration with CI/CD
```yaml
# GitHub Actions example
- name: Run Streaming Tests
  run: |
    python main.py &
    sleep 10  # Wait for server startup
    python run_streaming_tests.py
    pkill -f main.py
```

## 🎯 Expected Outcomes

After running tests successfully, you should see:

1. ✅ **All 3 agents** respond to WebSocket connections
2. ✅ **Tool calls stream properly** with start/progress/end messages  
3. ✅ **LLM responses stream** as coherent chunks
4. ✅ **Message ordering** is maintained (tools → LLM)
5. ✅ **Error handling** works gracefully
6. ✅ **Performance** meets defined thresholds
7. ✅ **Concurrent operations** don't interfere with each other

This validates that the streaming architecture works correctly for real-time financial analysis workflows.