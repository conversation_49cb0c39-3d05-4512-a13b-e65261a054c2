#!/usr/bin/env python3
"""
Comprehensive Agent Streaming Validation Test Suite

Tests tool call streaming and LLM streaming for MySQL agent, Storage Analyzer agent,
and Alert Manager agent through WebSocket connections.
"""

import asyncio
import json
import logging
import os
import sys
import time
import unittest
from typing import Dict, List, Any, Optional
from datetime import datetime
import concurrent.futures

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from streaming_test_client import (
    StreamingWebSocketClient,
    AgentStreamingValidator, 
    test_endpoint_streaming,
    StreamingValidationResult
)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BaseAgentStreamingTest(unittest.IsolatedAsyncioTestCase):
    """Base class for agent streaming tests."""
    
    def setUp(self):
        """Set up test environment."""
        # Load test scenarios
        self.test_scenarios = self.load_test_scenarios()
        self.base_url = "ws://localhost:8000"
        self.test_results = []
        
    def load_test_scenarios(self) -> Dict[str, Any]:
        """Load test scenarios from JSON file."""
        scenarios_file = os.path.join(os.path.dirname(__file__), "test_scenarios.json")
        try:
            with open(scenarios_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Test scenarios file not found: {scenarios_file}")
            return {}
            
    def validate_performance_metrics(
        self, 
        result: Dict[str, Any], 
        agent_type: str
    ) -> List[str]:
        """Validate performance metrics against benchmarks."""
        errors = []
        benchmarks = self.test_scenarios.get("performance_benchmarks", {})
        thresholds = benchmarks.get("latency_thresholds", {}).get(agent_type, {})
        
        performance = result.get("performance", {})
        
        # Check latency thresholds
        if "max_chunk_latency" in thresholds:
            if performance.get("average_chunk_latency", 0) > thresholds["max_chunk_latency"]:
                errors.append(
                    f"Average chunk latency {performance.get('average_chunk_latency'):.2f}s "
                    f"exceeds threshold {thresholds['max_chunk_latency']}s"
                )
                
        if "max_total_duration" in thresholds:
            if performance.get("total_duration", 0) > thresholds["max_total_duration"]:
                errors.append(
                    f"Total duration {performance.get('total_duration'):.2f}s "
                    f"exceeds threshold {thresholds['max_total_duration']}s"
                )
                
        return errors
        
    def assert_streaming_success(self, result: Dict[str, Any], test_name: str):
        """Assert that streaming test was successful."""
        self.assertTrue(result["success"], f"{test_name} failed: {result.get('errors', [])}")
        
        # Check minimum message counts
        msg_details = result.get("message_details", {})
        self.assertGreater(msg_details.get("total", 0), 0, f"{test_name}: No messages received")
        
        # Performance validation
        performance_errors = self.validate_performance_metrics(result, test_name.split("_")[0])
        if performance_errors:
            logger.warning(f"Performance issues in {test_name}: {performance_errors}")


class TestMySQLAgentStreaming(BaseAgentStreamingTest):
    """Test MySQL agent streaming functionality."""
    
    async def test_basic_table_query_streaming(self):
        """Test basic SQL table query with streaming validation."""
        scenario = self.test_scenarios["mysql_agent_scenarios"][0]  # basic_table_query
        
        result = await test_endpoint_streaming(
            endpoint=scenario["endpoint"],
            user_id="mysql_test_user",
            test_message=scenario["test_message"],
            connection_params=scenario["connection_params"],
            expected_tools=scenario["expected_tool_calls"],
            expected_chunks=scenario["expected_llm_chunks"]
        )
        
        self.test_results.append(result)
        self.assert_streaming_success(result, "mysql_basic_query")
        
        # MySQL-specific validations
        self.assertGreater(
            result["message_details"]["tool_calls"], 0,
            "No tool calls detected for MySQL query"
        )
        
        # Verify response contains expected content
        expected_content = scenario["validation_criteria"]["should_contain"]
        # Note: In real implementation, you'd check the actual response content
        
    async def test_data_analysis_streaming(self):
        """Test data analysis query with streaming validation."""
        scenario = self.test_scenarios["mysql_agent_scenarios"][1]  # data_analysis_query
        
        result = await test_endpoint_streaming(
            endpoint=scenario["endpoint"],
            user_id="mysql_analysis_user",
            test_message=scenario["test_message"],
            connection_params=scenario["connection_params"],
            expected_tools=scenario["expected_tool_calls"],
            expected_chunks=scenario["expected_llm_chunks"]
        )
        
        self.test_results.append(result)
        self.assert_streaming_success(result, "mysql_data_analysis")
        
    async def test_performance_analysis_streaming(self):
        """Test performance analysis query with streaming validation."""
        scenario = self.test_scenarios["mysql_agent_scenarios"][2]  # performance_analysis
        
        result = await test_endpoint_streaming(
            endpoint=scenario["endpoint"],
            user_id="mysql_perf_user",
            test_message=scenario["test_message"],
            connection_params=scenario["connection_params"],
            expected_tools=scenario["expected_tool_calls"],
            expected_chunks=scenario["expected_llm_chunks"]
        )
        
        self.test_results.append(result)
        self.assert_streaming_success(result, "mysql_performance_analysis")


class TestStorageAnalyzerStreaming(BaseAgentStreamingTest):
    """Test Storage Analyzer agent streaming functionality."""
    
    async def test_basic_shortage_analysis_streaming(self):
        """Test basic component shortage analysis with streaming."""
        scenario = self.test_scenarios["storage_analyzer_scenarios"][0]  # basic_shortage_analysis
        
        result = await test_endpoint_streaming(
            endpoint=scenario["endpoint"],
            user_id="storage_basic_user",
            test_message=scenario["test_message"],
            connection_params=scenario["connection_params"],
            expected_tools=scenario["expected_tool_calls"],
            expected_chunks=scenario["expected_llm_chunks"]
        )
        
        self.test_results.append(result)
        self.assert_streaming_success(result, "storage_analyzer_basic")
        
        # Storage-specific validations
        self.assertGreaterEqual(
            result["message_details"]["tool_calls"], 2,
            "Expected at least 2 tool calls for shortage analysis (ShortageIndex + WeightedShortageIndex)"
        )
        
    async def test_complex_multi_component_streaming(self):
        """Test complex multi-component analysis with streaming."""
        scenario = self.test_scenarios["storage_analyzer_scenarios"][1]  # complex_multi_component_analysis
        
        result = await test_endpoint_streaming(
            endpoint=scenario["endpoint"],
            user_id="storage_complex_user",
            test_message=scenario["test_message"],
            connection_params=scenario["connection_params"],
            expected_tools=scenario["expected_tool_calls"],
            expected_chunks=scenario["expected_llm_chunks"]
        )
        
        self.test_results.append(result)
        self.assert_streaming_success(result, "storage_analyzer_complex")
        
    async def test_supply_chain_stress_streaming(self):
        """Test supply chain stress scenario with streaming."""
        scenario = self.test_scenarios["storage_analyzer_scenarios"][2]  # supply_chain_stress_test
        
        result = await test_endpoint_streaming(
            endpoint=scenario["endpoint"],
            user_id="storage_stress_user",
            test_message=scenario["test_message"],
            connection_params=scenario["connection_params"],
            expected_tools=scenario["expected_tool_calls"],
            expected_chunks=scenario["expected_llm_chunks"]
        )
        
        self.test_results.append(result)
        self.assert_streaming_success(result, "storage_analyzer_stress")


class TestAlertManagerStreaming(BaseAgentStreamingTest):
    """Test Alert Manager agent streaming functionality."""
    
    async def test_critical_system_alert_streaming(self):
        """Test critical system alert processing with streaming."""
        scenario = self.test_scenarios["alert_manager_scenarios"][0]  # critical_system_alert
        
        result = await test_endpoint_streaming(
            endpoint=scenario["endpoint"],
            user_id="alert_critical_user",
            test_message=scenario["test_message"],
            connection_params=scenario["connection_params"],
            expected_tools=scenario["expected_tool_calls"],
            expected_chunks=scenario["expected_llm_chunks"]
        )
        
        self.test_results.append(result)
        self.assert_streaming_success(result, "alert_manager_critical")
        
        # Alert-specific validations
        self.assertGreater(
            result["message_details"]["tool_calls"], 0,
            "No tool calls detected for alert processing"
        )
        
    async def test_multi_channel_notification_streaming(self):
        """Test multi-channel notification with streaming."""
        scenario = self.test_scenarios["alert_manager_scenarios"][1]  # multi_channel_notification
        
        result = await test_endpoint_streaming(
            endpoint=scenario["endpoint"],
            user_id="alert_multichannel_user",
            test_message=scenario["test_message"],
            connection_params=scenario["connection_params"],
            expected_tools=scenario["expected_tool_calls"],
            expected_chunks=scenario["expected_llm_chunks"]
        )
        
        self.test_results.append(result)
        self.assert_streaming_success(result, "alert_manager_multichannel")
        
    async def test_alert_aggregation_workflow_streaming(self):
        """Test alert aggregation workflow with streaming."""
        scenario = self.test_scenarios["alert_manager_scenarios"][2]  # alert_aggregation_workflow
        
        result = await test_endpoint_streaming(
            endpoint=scenario["endpoint"],
            user_id="alert_workflow_user", 
            test_message=scenario["test_message"],
            connection_params=scenario["connection_params"],
            expected_tools=scenario["expected_tool_calls"],
            expected_chunks=scenario["expected_llm_chunks"]
        )
        
        self.test_results.append(result)
        self.assert_streaming_success(result, "alert_manager_workflow")


class TestConcurrentAndErrorHandling(BaseAgentStreamingTest):
    """Test concurrent streaming and error handling."""
    
    async def test_concurrent_agent_streaming(self):
        """Test all three agents streaming concurrently."""
        concurrent_scenario = self.test_scenarios["concurrent_test_scenarios"][0]
        test_cases = concurrent_scenario["test_cases"]
        
        # Run all agents concurrently
        tasks = []
        for i, test_case in enumerate(test_cases):
            task = test_endpoint_streaming(
                endpoint=test_case["endpoint"],
                user_id=f"concurrent_{test_case['agent']}_{i}",
                test_message=test_case["test_message"],
                connection_params=test_case["connection_params"],
                expected_tools=2,  # Default expectation
                expected_chunks=3   # Default expectation
            )
            tasks.append(task)
            
        # Wait for all concurrent tests to complete
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        duration = time.time() - start_time
        
        # Validate concurrent execution
        max_duration = concurrent_scenario["validation_criteria"]["max_concurrent_duration"]
        self.assertLess(duration, max_duration, 
                       f"Concurrent execution took {duration:.2f}s, expected < {max_duration}s")
        
        # Validate success rate
        successful_results = [r for r in results if isinstance(r, dict) and r.get("success", False)]
        success_rate = len(successful_results) / len(results)
        min_success_rate = concurrent_scenario["validation_criteria"]["min_success_rate"]
        self.assertGreaterEqual(success_rate, min_success_rate,
                              f"Success rate {success_rate:.2f} below threshold {min_success_rate}")
        
        # Store results
        self.test_results.extend([r for r in results if isinstance(r, dict)])
        
    async def test_connection_error_handling(self):
        """Test connection error handling."""
        # Test connection to non-existent endpoint
        validator = AgentStreamingValidator("ws://localhost:9999")  # Wrong port
        
        result = await validator.validate_agent_streaming(
            endpoint="/ws/mysql",
            user_id="error_test_user",
            test_message="Test message",
            expected_tool_calls=1,
            expected_llm_chunks=1
        )
        
        # Should fail gracefully
        self.assertFalse(result.success)
        self.assertIn("Failed to connect", str(result.errors))
        
    async def test_message_format_error_handling(self):
        """Test handling of invalid message formats."""
        client = StreamingWebSocketClient()
        
        # This would normally test invalid JSON, but requires server setup
        # For now, we'll test with a basic validator setup
        validator = AgentStreamingValidator()
        
        # Test with malformed endpoint 
        result = await validator.validate_agent_streaming(
            endpoint="/ws/nonexistent",
            user_id="format_test_user",
            test_message="Test message",
            expected_tool_calls=0,
            expected_llm_chunks=0
        )
        
        # Should handle gracefully
        self.assertFalse(result.success)


class TestPerformanceBenchmarks(BaseAgentStreamingTest):
    """Test performance benchmarks for streaming."""
    
    async def test_latency_benchmarks(self):
        """Test latency benchmarks for all agents."""
        benchmarks = self.test_scenarios.get("performance_benchmarks", {})
        thresholds = benchmarks.get("latency_thresholds", {})
        
        # Quick test for each agent type
        test_cases = [
            ("mysql", "/ws/mysql", {"query": "SELECT 1"}, "Quick MySQL test"),
            ("storage_analyzer", "/ws/storage", {"company": "QuickTest"}, "Quick storage test"),
            ("alert_manager", "/ws/alert", {"company": "QuickTest"}, "Quick alert test")
        ]
        
        for agent_type, endpoint, params, message in test_cases:
            if agent_type in thresholds:
                start_time = time.time()
                result = await test_endpoint_streaming(
                    endpoint=endpoint,
                    user_id=f"perf_{agent_type}_user",
                    test_message=message,
                    connection_params=params,
                    expected_tools=1,
                    expected_chunks=2
                )
                
                # Validate against thresholds
                threshold = thresholds[agent_type]
                performance = result["performance"]
                
                if "max_total_duration" in threshold:
                    self.assertLess(
                        performance["total_duration"],
                        threshold["max_total_duration"],
                        f"{agent_type} total duration exceeded threshold"
                    )
                    
                self.test_results.append(result)


def generate_test_report(test_results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Generate comprehensive test report."""
    report = {
        "test_execution_time": datetime.now().isoformat(),
        "total_tests": len(test_results),
        "successful_tests": len([r for r in test_results if r.get("success", False)]),
        "failed_tests": len([r for r in test_results if not r.get("success", False)]),
        "success_rate": 0,
        "agent_performance": {},
        "overall_performance": {},
        "test_details": test_results
    }
    
    if report["total_tests"] > 0:
        report["success_rate"] = report["successful_tests"] / report["total_tests"]
        
    # Calculate performance metrics
    successful_results = [r for r in test_results if r.get("success", False)]
    if successful_results:
        durations = [r["performance"]["total_duration"] for r in successful_results]
        latencies = [r["performance"]["average_chunk_latency"] for r in successful_results]
        
        report["overall_performance"] = {
            "average_duration": sum(durations) / len(durations),
            "average_latency": sum(latencies) / len(latencies),
            "min_duration": min(durations),
            "max_duration": max(durations)
        }
        
    # Agent-specific performance
    agents = ["mysql", "storage", "alert"]
    for agent in agents:
        agent_results = [r for r in successful_results if agent in r["endpoint"]]
        if agent_results:
            durations = [r["performance"]["total_duration"] for r in agent_results]
            report["agent_performance"][agent] = {
                "test_count": len(agent_results),
                "average_duration": sum(durations) / len(durations),
                "success_rate": len(agent_results) / len([r for r in test_results if agent in r["endpoint"]])
            }
    
    return report


async def run_comprehensive_streaming_tests():
    """Run comprehensive streaming validation tests."""
    logger.info("Starting comprehensive agent streaming validation tests...")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add all test classes
    test_classes = [
        TestMySQLAgentStreaming,
        TestStorageAnalyzerStreaming,
        TestAlertManagerStreaming,
        TestConcurrentAndErrorHandling,
        TestPerformanceBenchmarks
    ]
    
    all_results = []
    
    # Run each test class
    for test_class in test_classes:
        logger.info(f"Running {test_class.__name__}...")
        
        # Create test instance
        test_instance = test_class()
        test_instance.setUp()
        
        # Run all test methods
        test_methods = [method for method in dir(test_instance) 
                      if method.startswith('test_') and callable(getattr(test_instance, method))]
                      
        for method_name in test_methods:
            try:
                logger.info(f"  Running {method_name}...")
                test_method = getattr(test_instance, method_name)
                await test_method()
                all_results.extend(test_instance.test_results)
            except Exception as e:
                logger.error(f"  Test {method_name} failed: {e}")
                # Add failure to results
                all_results.append({
                    "endpoint": f"error_{method_name}",
                    "success": False,
                    "errors": [str(e)],
                    "performance": {"total_duration": 0, "average_chunk_latency": 0},
                    "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}
                })
    
    # Generate final report
    report = generate_test_report(all_results)
    
    # Save report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"streaming_validation_report_{timestamp}.json"
    
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
        
    logger.info(f"Test report saved to {report_file}")
    logger.info(f"Test Summary: {report['successful_tests']}/{report['total_tests']} tests passed "
               f"({report['success_rate']:.1%} success rate)")
    
    return report


if __name__ == "__main__":
    asyncio.run(run_comprehensive_streaming_tests())