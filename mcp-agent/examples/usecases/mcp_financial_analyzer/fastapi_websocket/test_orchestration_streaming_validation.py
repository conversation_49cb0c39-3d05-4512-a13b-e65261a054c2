#!/usr/bin/env python3
"""
Comprehensive test script to verify real-time streaming capabilities of the orchestrated shortage workflow.

This test validates:
1. Real-time LLM text streaming (chunks appear as generated, not batched)
2. Real-time MCP tool call event streaming (immediate tool events)
3. Orchestrated workflow verification (MySQL → Shortage → Alert pipeline)
4. WebSocket connection persistence throughout workflow
5. Streaming performance matching individual MySQL agent endpoint

Usage:
    python test_orchestration_streaming_validation.py
"""

import asyncio
import json
import logging
import time
import websockets
from datetime import datetime
from typing import Dict, List, Any, Optional
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OrchestrationStreamingValidator:
    """Validates real-time streaming capabilities of orchestrated shortage workflow."""
    
    def __init__(self, server_url: str = "ws://localhost:8000"):
        self.server_url = server_url
        self.test_results = {
            "test_start_time": datetime.now().isoformat(),
            "streaming_metrics": {},
            "tool_events": [],
            "text_chunks": [],
            "workflow_stages": [],
            "errors": [],
            "success": False
        }
        
    async def test_orchestration_streaming(self, user_id: str = "test_streaming_user") -> Dict[str, Any]:
        """Test the orchestrated shortage workflow streaming capabilities."""
        logger.info("Starting orchestration streaming validation test")
        
        # Test message that triggers the full MySQL → Shortage → Alert pipeline
        test_message = {
            "message": "CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION.",
            "streaming": True
        }
        
        orchestration_url = f"{self.server_url}/ws/orchestration/{user_id}?mode=pattern_based"
        
        try:
            logger.info(f"Connecting to orchestration endpoint: {orchestration_url}")
            async with websockets.connect(orchestration_url) as websocket:
                logger.info("✓ WebSocket connection established")
                
                # Track streaming metrics
                stream_start_time = None
                first_chunk_time = None
                last_chunk_time = None
                chunk_count = 0
                tool_event_count = 0
                workflow_stage_count = 0
                
                # Send test message
                logger.info("Sending test message for orchestrated workflow")
                await websocket.send(json.dumps(test_message))
                
                # Collect streaming events
                while True:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=60.0)
                        event_time = time.time()
                        data = json.loads(response)
                        event_type = data.get("type", "unknown")
                        
                        logger.info(f"Received event: {event_type}")
                        
                        if event_type == "system":
                            # Welcome message
                            logger.info(f"System message: {data.get('message', '')}")
                            
                        elif event_type == "stream_start":
                            stream_start_time = event_time
                            logger.info("🚀 Stream started")
                            self.test_results["workflow_stages"].append({
                                "stage": "stream_start",
                                "timestamp": event_time,
                                "message": data.get("message", "")
                            })
                            
                        elif event_type == "stream_chunk":
                            chunk_count += 1
                            if first_chunk_time is None:
                                first_chunk_time = event_time
                            last_chunk_time = event_time
                            
                            chunk_text = data.get("message", "")
                            logger.info(f"📦 Chunk #{chunk_count}: {chunk_text[:50]}{'...' if len(chunk_text) > 50 else ''}")
                            
                            self.test_results["text_chunks"].append({
                                "chunk_number": chunk_count,
                                "timestamp": event_time,
                                "text": chunk_text,
                                "length": len(chunk_text)
                            })
                            
                            # Check for workflow stage indicators in text
                            if any(indicator in chunk_text.lower() for indicator in ["mysql", "database", "step 1"]):
                                workflow_stage_count += 1
                                self.test_results["workflow_stages"].append({
                                    "stage": "mysql_analysis",
                                    "timestamp": event_time,
                                    "detected_in_chunk": chunk_count
                                })
                            elif any(indicator in chunk_text.lower() for indicator in ["shortage", "analysis", "step 2"]):
                                workflow_stage_count += 1
                                self.test_results["workflow_stages"].append({
                                    "stage": "shortage_analysis", 
                                    "timestamp": event_time,
                                    "detected_in_chunk": chunk_count
                                })
                            elif any(indicator in chunk_text.lower() for indicator in ["alert", "notification", "step 3"]):
                                workflow_stage_count += 1
                                self.test_results["workflow_stages"].append({
                                    "stage": "alert_management",
                                    "timestamp": event_time,
                                    "detected_in_chunk": chunk_count
                                })
                            
                        elif event_type in ["mcp_tool_stream", "tool_stream"]:
                            tool_event_count += 1
                            tool_data = data.get("data", {})
                            tool_name = tool_data.get("tool_name", "unknown")
                            tool_status = tool_data.get("type", "unknown")
                            
                            logger.info(f"🔧 Tool event #{tool_event_count}: {tool_name} - {tool_status}")
                            
                            self.test_results["tool_events"].append({
                                "event_number": tool_event_count,
                                "timestamp": event_time,
                                "tool_name": tool_name,
                                "status": tool_status,
                                "data": tool_data
                            })
                            
                        elif event_type == "stream_end":
                            logger.info("🏁 Stream ended")
                            self.test_results["workflow_stages"].append({
                                "stage": "stream_end",
                                "timestamp": event_time,
                                "message": data.get("message", "")
                            })
                            break
                            
                        elif event_type == "error":
                            error_msg = data.get("message", "Unknown error")
                            logger.error(f"❌ Error: {error_msg}")
                            self.test_results["errors"].append({
                                "timestamp": event_time,
                                "error": error_msg
                            })
                            break
                            
                    except asyncio.TimeoutError:
                        logger.error("❌ Timeout waiting for response")
                        self.test_results["errors"].append({
                            "timestamp": time.time(),
                            "error": "Timeout waiting for response"
                        })
                        break
                        
                # Calculate streaming metrics
                if stream_start_time and first_chunk_time:
                    time_to_first_chunk = first_chunk_time - stream_start_time
                    total_stream_time = last_chunk_time - stream_start_time if last_chunk_time else 0
                    
                    self.test_results["streaming_metrics"] = {
                        "time_to_first_chunk_seconds": time_to_first_chunk,
                        "total_stream_time_seconds": total_stream_time,
                        "total_chunks": chunk_count,
                        "total_tool_events": tool_event_count,
                        "workflow_stages_detected": workflow_stage_count,
                        "chunks_per_second": chunk_count / total_stream_time if total_stream_time > 0 else 0,
                        "tool_events_per_second": tool_event_count / total_stream_time if total_stream_time > 0 else 0
                    }
                    
                logger.info("✓ WebSocket connection closed successfully")
                
        except Exception as e:
            logger.error(f"❌ Connection error: {e}")
            self.test_results["errors"].append({
                "timestamp": time.time(),
                "error": f"Connection error: {str(e)}"
            })
            return self.test_results
            
        # Evaluate test results
        self.test_results["success"] = self._evaluate_streaming_performance()
        self.test_results["test_end_time"] = datetime.now().isoformat()
        
        return self.test_results
        
    def _evaluate_streaming_performance(self) -> bool:
        """Evaluate if streaming performance meets requirements."""
        metrics = self.test_results["streaming_metrics"]
        
        if not metrics:
            logger.error("❌ No streaming metrics collected")
            return False
            
        # Performance criteria
        criteria = {
            "time_to_first_chunk": 5.0,  # Should get first chunk within 5 seconds
            "minimum_chunks": 10,         # Should receive at least 10 text chunks
            "minimum_tool_events": 3,     # Should receive at least 3 tool events
            "minimum_workflow_stages": 2, # Should detect at least 2 workflow stages
            "no_errors": True             # Should have no errors
        }
        
        results = []
        
        # Check time to first chunk
        if metrics.get("time_to_first_chunk_seconds", float('inf')) <= criteria["time_to_first_chunk"]:
            results.append(True)
            logger.info(f"✓ Time to first chunk: {metrics['time_to_first_chunk_seconds']:.2f}s (≤ {criteria['time_to_first_chunk']}s)")
        else:
            results.append(False)
            logger.error(f"❌ Time to first chunk too slow: {metrics.get('time_to_first_chunk_seconds', 'N/A')}s")
            
        # Check minimum chunks
        if metrics.get("total_chunks", 0) >= criteria["minimum_chunks"]:
            results.append(True)
            logger.info(f"✓ Text chunks received: {metrics['total_chunks']} (≥ {criteria['minimum_chunks']})")
        else:
            results.append(False)
            logger.error(f"❌ Insufficient text chunks: {metrics.get('total_chunks', 0)}")
            
        # Check tool events
        if metrics.get("total_tool_events", 0) >= criteria["minimum_tool_events"]:
            results.append(True)
            logger.info(f"✓ Tool events received: {metrics['total_tool_events']} (≥ {criteria['minimum_tool_events']})")
        else:
            results.append(False)
            logger.error(f"❌ Insufficient tool events: {metrics.get('total_tool_events', 0)}")
            
        # Check workflow stages
        if metrics.get("workflow_stages_detected", 0) >= criteria["minimum_workflow_stages"]:
            results.append(True)
            logger.info(f"✓ Workflow stages detected: {metrics['workflow_stages_detected']} (≥ {criteria['minimum_workflow_stages']})")
        else:
            results.append(False)
            logger.error(f"❌ Insufficient workflow stages: {metrics.get('workflow_stages_detected', 0)}")
            
        # Check for errors
        if len(self.test_results["errors"]) == 0:
            results.append(True)
            logger.info("✓ No errors encountered")
        else:
            results.append(False)
            logger.error(f"❌ Errors encountered: {len(self.test_results['errors'])}")
            
        return all(results)
        
    def generate_report(self) -> str:
        """Generate a comprehensive test report."""
        report = []
        report.append("=" * 80)
        report.append("ORCHESTRATION STREAMING VALIDATION REPORT")
        report.append("=" * 80)
        report.append(f"Test Start: {self.test_results['test_start_time']}")
        report.append(f"Test End: {self.test_results.get('test_end_time', 'N/A')}")
        report.append(f"Overall Success: {'✓ PASS' if self.test_results['success'] else '❌ FAIL'}")
        report.append("")
        
        # Streaming metrics
        metrics = self.test_results["streaming_metrics"]
        if metrics:
            report.append("STREAMING PERFORMANCE METRICS:")
            report.append("-" * 40)
            report.append(f"Time to First Chunk: {metrics.get('time_to_first_chunk_seconds', 'N/A'):.2f}s")
            report.append(f"Total Stream Time: {metrics.get('total_stream_time_seconds', 'N/A'):.2f}s")
            report.append(f"Text Chunks: {metrics.get('total_chunks', 'N/A')}")
            report.append(f"Tool Events: {metrics.get('total_tool_events', 'N/A')}")
            report.append(f"Workflow Stages: {metrics.get('workflow_stages_detected', 'N/A')}")
            report.append(f"Chunks/Second: {metrics.get('chunks_per_second', 'N/A'):.2f}")
            report.append(f"Tool Events/Second: {metrics.get('tool_events_per_second', 'N/A'):.2f}")
            report.append("")
        
        # Workflow stages
        if self.test_results["workflow_stages"]:
            report.append("WORKFLOW STAGE PROGRESSION:")
            report.append("-" * 40)
            for stage in self.test_results["workflow_stages"]:
                report.append(f"  {stage['stage']}: {stage.get('message', '')}")
            report.append("")
        
        # Tool events summary
        if self.test_results["tool_events"]:
            report.append("TOOL EVENTS SUMMARY:")
            report.append("-" * 40)
            tool_summary = {}
            for event in self.test_results["tool_events"]:
                tool_name = event["tool_name"]
                status = event["status"]
                key = f"{tool_name}:{status}"
                tool_summary[key] = tool_summary.get(key, 0) + 1
            
            for tool_status, count in tool_summary.items():
                report.append(f"  {tool_status}: {count}")
            report.append("")
        
        # Errors
        if self.test_results["errors"]:
            report.append("ERRORS ENCOUNTERED:")
            report.append("-" * 40)
            for error in self.test_results["errors"]:
                report.append(f"  {error['error']}")
            report.append("")
        
        return "\n".join(report)


async def main():
    """Main test execution function."""
    print("🧪 Starting Orchestration Streaming Validation Test")
    print("=" * 60)
    
    validator = OrchestrationStreamingValidator()
    
    try:
        # Run the streaming validation test
        results = await validator.test_orchestration_streaming()
        
        # Generate and display report
        report = validator.generate_report()
        print(report)
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"orchestration_streaming_validation_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"📄 Detailed results saved to: {results_file}")
        
        # Exit with appropriate code
        exit_code = 0 if results["success"] else 1
        print(f"\n🏁 Test completed with exit code: {exit_code}")
        return exit_code
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        print(f"\n❌ Test execution failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
