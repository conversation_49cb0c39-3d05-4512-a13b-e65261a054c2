#!/usr/bin/env python3
"""
Test Setup Validation Script

Validates that all test files are properly configured and imports work correctly.
"""

import json
import os
import sys
import importlib.util


def check_file_exists(filepath: str, description: str) -> bool:
    """Check if file exists and report status."""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} - NOT FOUND")
        return False


def check_json_validity(filepath: str, description: str) -> bool:
    """Check if JSON file is valid."""
    try:
        with open(filepath, 'r') as f:
            json.load(f)
        print(f"✅ {description}: Valid JSON")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ {description}: Invalid JSON - {e}")
        return False
    except FileNotFoundError:
        print(f"❌ {description}: File not found")
        return False


def check_python_imports(filepath: str, description: str) -> bool:
    """Check if Python file imports successfully."""
    try:
        spec = importlib.util.spec_from_file_location("test_module", filepath)
        module = importlib.util.module_from_spec(spec)
        # Don't execute, just check if it would import
        print(f"✅ {description}: Python imports OK")
        return True
    except Exception as e:
        print(f"❌ {description}: Import error - {e}")
        return False


def main():
    """Main validation function."""
    print("🔍 Validating Test Setup for Agent Streaming Tests\n")
    
    success_count = 0
    total_checks = 0
    
    # Check core files exist
    files_to_check = [
        ("streaming_test_client.py", "Streaming Test Client"),
        ("test_scenarios.json", "Test Scenarios Data"),  
        ("test_agent_streaming_validation.py", "Main Test Suite"),
        ("run_streaming_tests.py", "Test Runner Script"),
        ("STREAMING_TESTS_README.md", "Documentation")
    ]
    
    for filepath, description in files_to_check:
        if check_file_exists(filepath, description):
            success_count += 1
        total_checks += 1
    
    print()
    
    # Check JSON validity
    if os.path.exists("test_scenarios.json"):
        if check_json_validity("test_scenarios.json", "Test Scenarios JSON"):
            success_count += 1
        total_checks += 1
        
        # Check JSON structure
        try:
            with open("test_scenarios.json", 'r') as f:
                data = json.load(f)
                required_sections = [
                    "mysql_agent_scenarios",
                    "storage_analyzer_scenarios", 
                    "alert_manager_scenarios"
                ]
                for section in required_sections:
                    if section in data:
                        print(f"✅ Test scenarios include {section}")
                        success_count += 1
                    else:
                        print(f"❌ Missing section: {section}")
                    total_checks += 1
        except:
            pass
    
    print()
    
    # Check Python imports (basic validation)
    python_files = [
        ("streaming_test_client.py", "Streaming Test Client"),
        ("test_agent_streaming_validation.py", "Main Test Suite"),
        ("run_streaming_tests.py", "Test Runner")
    ]
    
    for filepath, description in python_files:
        if os.path.exists(filepath):
            if check_python_imports(filepath, description):
                success_count += 1
            total_checks += 1
    
    print()
    
    # Summary
    success_rate = success_count / total_checks if total_checks > 0 else 0
    if success_rate >= 0.9:
        status = "✅ SETUP VALID"
        color = "\033[92m"
    elif success_rate >= 0.7:
        status = "⚠️  SETUP PARTIAL"
        color = "\033[93m"
    else:
        status = "❌ SETUP INVALID"
        color = "\033[91m"
    
    reset = "\033[0m"
    
    print(f"{color}{'='*50}{reset}")
    print(f"{color}{status}: {success_count}/{total_checks} checks passed{reset}")
    print(f"{color}{'='*50}{reset}")
    
    if success_rate >= 0.9:
        print("\n🚀 Ready to run streaming tests!")
        print("   Usage: python run_streaming_tests.py")
    else:
        print("\n🛠️  Please fix the above issues before running tests.")
        
    return success_rate >= 0.9


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Validation interrupted")
        sys.exit(1)