#!/usr/bin/env python3
"""Streaming demo client for the MCP Financial Analyzer WebSocket endpoints."""

from __future__ import annotations

import argparse
import asyncio
import json
import sys
from collections import Counter
from dataclasses import dataclass, field
from time import perf_counter
from typing import Any, Dict, List, Optional, Sequence, Tuple
from urllib.parse import quote_plus, urlencode

import websockets
from websockets import ConnectionClosed, WebSocketClientProtocol


@dataclass
class StreamTracking:
    """Track LLM chunks and tool events while a response streams."""

    chunks: List[str] = field(default_factory=list)
    tool_events: List[Dict[str, Any]] = field(default_factory=list)
    tool_call_durations: List[float] = field(default_factory=list)
    message_counts: Counter[str] = field(default_factory=Counter)
    stream_started: bool = False
    stream_completed: bool = False
    stream_started_at: Optional[float] = None
    first_chunk_at: Optional[float] = None
    last_chunk_at: Optional[float] = None
    first_tool_event_at: Optional[float] = None
    last_tool_event_at: Optional[float] = None
    stream_completed_at: Optional[float] = None
    full_response: Optional[str] = None


@dataclass
class ValidationConfig:
    """Minimum streaming expectations for a run."""

    min_chunks: int = 1
    min_tool_events: int = 1


@dataclass
class WorkflowStage:
    """Track status for an individual stage within a workflow."""

    name: str
    keywords: Tuple[str, ...]
    started: bool = False
    completed: bool = False
    error: Optional[str] = None
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    last_tool_name: Optional[str] = None


class ToolCallTracker:
    """Track individual tool call lifecycles to measure durations."""

    def __init__(self) -> None:
        self._active: Dict[str, List[float]] = {}

    def reset(self) -> None:
        """Clear any active tool state before a new streaming run."""
        self._active.clear()

    def record_start(self, tool_name: str, started_at: float) -> None:
        key = tool_name or "unknown_tool"
        self._active.setdefault(key, []).append(started_at)

    def record_finish(self, tool_name: str, finished_at: float) -> Optional[float]:
        key = tool_name or "unknown_tool"
        queue = self._active.get(key)
        if not queue:
            return None
        start_time = queue.pop(0)
        if not queue:
            self._active.pop(key, None)
        duration = finished_at - start_time
        if duration < 0:
            return 0.0
        return duration


class WorkflowProgressTracker:
    """Observe MCP tool events and emit human-readable workflow progress."""

    def __init__(
        self,
        stages: Sequence[Tuple[str, Sequence[str]]],
        now_fn,
    ) -> None:
        self._now = now_fn
        self._stages: List[WorkflowStage] = [
            WorkflowStage(name=name, keywords=tuple(keyword.lower() for keyword in keywords))
            for name, keywords in stages
        ]

    def describe_plan(self) -> str:
        sequence = " -> ".join(stage.name for stage in self._stages)
        return f"[workflow] Planned sequence: {sequence}"

    def record_event(self, tool_name: str, event_type: str) -> List[str]:
        tool_name = (tool_name or "").strip()
        event_type = (event_type or "").strip()
        if not tool_name or not event_type:
            return []

        stage = self._find_stage(tool_name)
        if stage is None:
            return []

        timestamp = self._now()
        lower_event = event_type.lower()

        messages: List[str] = []

        if "error" in lower_event:
            stage.started = True
            stage.error = f"{tool_name}: {event_type}"
            stage.last_tool_name = tool_name
            stage.completed = False
            messages.append(
                f"[workflow] {stage.name} stage reported an error via '{tool_name}'"
            )
        elif "start" in lower_event:
            if not stage.started:
                stage.started = True
                stage.started_at = timestamp
                stage.last_tool_name = tool_name
                messages.append(
                    f"[workflow] {stage.name} stage started via '{tool_name}'"
                )
            else:
                stage.last_tool_name = tool_name
        elif any(keyword in lower_event for keyword in ("result", "complete", "finish")):
            if not stage.started:
                stage.started = True
                stage.started_at = timestamp
            if not stage.completed:
                stage.completed = True
                stage.completed_at = timestamp
                stage.last_tool_name = tool_name
                if stage.started_at is not None:
                    duration = timestamp - stage.started_at
                    messages.append(
                        f"[workflow] {stage.name} stage completed via '{tool_name}' in {duration:.2f}s"
                    )
                else:
                    messages.append(
                        f"[workflow] {stage.name} stage completed via '{tool_name}'"
                    )
        else:
            return []

        if not messages:
            return []

        messages.append(self._progress_line())
        return messages

    def finalize(self) -> List[str]:
        messages: List[str] = [self._progress_line()]

        errors = [stage.error for stage in self._stages if stage.error]
        incomplete = [stage.name for stage in self._stages if not stage.completed]

        if errors:
            error_text = "; ".join(error for error in errors if error)
            messages.append(f"[workflow] Workflow completed with errors: {error_text}")
        elif incomplete:
            pending = ", ".join(incomplete)
            messages.append(f"[workflow] Workflow ended before completing: {pending}")
        else:
            start_time = next((stage.started_at for stage in self._stages if stage.started_at is not None), None)
            end_time = next((stage.completed_at for stage in reversed(self._stages) if stage.completed_at is not None), None)
            if start_time is not None and end_time is not None and end_time >= start_time:
                messages.append(f"[workflow] Workflow completed in {end_time - start_time:.2f}s")
            else:
                messages.append("[workflow] Workflow completed")

        return messages

    def _find_stage(self, tool_name: str) -> Optional[WorkflowStage]:
        lowered = tool_name.lower()
        for stage in self._stages:
            if any(keyword in lowered for keyword in stage.keywords):
                return stage
        return None

    def _progress_line(self) -> str:
        def format_stage(stage: WorkflowStage) -> str:
            if stage.error:
                return f"{stage.name} ✖"
            if stage.completed:
                return f"{stage.name} ✅"
            if stage.started:
                return f"{stage.name} …"
            return f"{stage.name} ○"

        return "[workflow] Progress: " + " -> ".join(format_stage(stage) for stage in self._stages)


DEFAULT_SHORTAGE_MESSAGE = (
    "CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring "
    "MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), "
    "ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY "
    "NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."
)

DEFAULT_ALERT_MESSAGE = (
    "<reflection>ALERT MANAGER VALIDATION</reflection> IMMEDIATE ESCALATION: "
    "Evaluate shortage signals for Tech Pioneer Co., Ltd., confirm risk level, "
    "and invoke the REAL MCP TOOL notification workflow with CRITICAL priority "
    "so customer and management receive urgent delivery warnings."
)

DEFAULT_SQL_MESSAGE = "SHOW TABLES;"

DEFAULT_SHORTAGE_WORKFLOW_QUERY = (
    "Trigger the orchestrated shortage workflow: query MySQL for customer order "
    "CUSTORD-202506001 from Tech Pioneer Co., Ltd. The order needs 4000 units of "
    "GPU MM2004IC001, 2000 units of CPU DEP2004IC001, and 8000 units of memory "
    "ATR6G00801. Produce shortage indices and escalate a critical alert with "
    "urgent notification if inventory is insufficient."
)

SHORTAGE_WORKFLOW_STAGES: Sequence[Tuple[str, Sequence[str]]] = (
    ("MySQL", ("mysql", "database", "sql")),
    ("Shortage", ("shortage", "storage", "shortage-index")),
    ("Alert", ("alert", "notification", "escalation")),
)


class MCPStreamingClient:
    """Streaming client that exercises MCP endpoints and prints live updates."""

    def __init__(
        self,
        host: str,
        port: int,
        user_id: str,
        endpoint: str,
        query_params: Optional[Dict[str, str]],
        message: str,
        timeout: Optional[float],
        validation: ValidationConfig,
        workflow_stages: Optional[Sequence[Tuple[str, Sequence[str]]]] = None,
    ) -> None:
        self._host = host
        self._port = port
        self._user_id = user_id
        self._requested_endpoint = endpoint
        self._endpoint = self.normalize_endpoint(endpoint)
        self._query_params = {
            key: value
            for key, value in (query_params or {}).items()
            if value is not None and value != ""
        }
        self._message = message
        self._timeout = timeout
        self._validation = validation

        self._websocket: Optional[WebSocketClientProtocol] = None
        self._state = StreamTracking()
        self._last_chunk_ended_with_newline = True
        self._validation_failed = False
        self._workflow_tracker = (
            WorkflowProgressTracker(workflow_stages, self._now) if workflow_stages else None
        )
        self._tool_tracker = ToolCallTracker()

        if self._workflow_tracker:
            print(self._workflow_tracker.describe_plan())

    @property
    def uri(self) -> str:
        base_uri = f"ws://{self._host}:{self._port}/ws/{self._endpoint}/{self._user_id}"
        if not self._query_params:
            return base_uri
        encoded = urlencode(self._query_params, doseq=True, quote_via=quote_plus)
        return f"{base_uri}?{encoded}"

    @staticmethod
    def normalize_endpoint(endpoint: Optional[str]) -> str:
        normalized = (endpoint or "storage").strip("/").lower()
        if not normalized:
            return "storage"
        alias_map = {
            "storage_analyzer": "storage",
            "shortage": "storage",
            "shortage_analyzer": "storage",
            "alert_manager": "alert",
            "alerts": "alert",
            "shortage_workflow": "orchestration",
        }
        return alias_map.get(normalized, normalized)

    @property
    def validation_failed(self) -> bool:
        return self._validation_failed

    async def connect(self) -> None:
        """Open the WebSocket connection."""
        self._websocket = await websockets.connect(
            self.uri,
            ping_interval=None,
            ping_timeout=None,
            max_size=None,
        )
        print(f"[connect] Connected to {self.uri}")

    @staticmethod
    def _now() -> float:
        try:
            return asyncio.get_running_loop().time()
        except RuntimeError:
            return perf_counter()

    async def disconnect(self) -> None:
        """Close the WebSocket connection if it is still open."""
        ws = self._websocket
        self._websocket = None
        if not ws:
            return
        try:
            await ws.close()
        except Exception as exc:  # pragma: no cover - close failures are non-fatal
            print(f"[warning] Failed to close WebSocket cleanly: {exc}")
        else:
            print("[connect] WebSocket closed")

    async def send_message(self, text: str) -> None:
        """Send a message to the server with streaming enabled."""
        if not self._websocket:
            raise RuntimeError("WebSocket is not connected")
        payload = {"message": text, "streaming": True}
        await self._websocket.send(json.dumps(payload))
        print(f"[send] {text}")

    async def run(self) -> None:
        """Connect, send the requested message, and process streaming output."""
        try:
            await self.connect()
        except Exception as exc:  # pragma: no cover - connection failure is surfaced to user
            print(f"[error] Failed to connect: {exc}")
            return

        try:
            if self._message:
                await self.send_message(self._message)
            await self._receive_loop()
        finally:
            if self._validation.min_chunks > 0 and not self._state.stream_completed:
                print("[validation] Stream did not reach stream_end")
                self._validation_failed = True
            await self.disconnect()

    async def _receive_loop(self) -> None:
        if not self._websocket:
            raise RuntimeError("WebSocket is not connected")

        while True:
            try:
                raw_message = await self._receive_text()
            except asyncio.TimeoutError:
                print("[timeout] No data received within the specified timeout")
                return
            except ConnectionClosed as exc:
                print(f"[disconnect] Connection closed ({exc.code}: {exc.reason})")
                return

            data = self._parse_json(raw_message)
            if data is None:
                continue

            message_type = str(data.get("type", "") or "unknown")
            self._state.message_counts[message_type] += 1

            if message_type == "stream_start":
                self._handle_stream_start(data)
            elif message_type == "stream_chunk":
                self._handle_stream_chunk(data)
            elif message_type == "stream_end":
                self._handle_stream_end(data)
                return
            elif message_type in {"mcp_tool_stream", "tool_stream"}:
                self._handle_tool_stream(data.get("data"), message_type)
            elif message_type == "system":
                self._log_line("system", data.get("message"))
            elif message_type == "progress":
                self._log_line("progress", data.get("message"))
            elif message_type == "result":
                self._log_line("result", data.get("message"))
                self._flag_non_streaming("result")
                return
            elif message_type == "error":
                self._log_line("error", data.get("message"))
                self._flag_non_streaming("error")
                return
            else:
                self._log_line(message_type, data)

    async def _receive_text(self) -> str:
        assert self._websocket is not None
        if self._timeout and self._timeout > 0:
            return await asyncio.wait_for(self._websocket.recv(), timeout=self._timeout)
        return await self._websocket.recv()

    @staticmethod
    def _parse_json(raw_message: Any) -> Optional[Dict[str, Any]]:
        try:
            return json.loads(raw_message)
        except json.JSONDecodeError:
            print(f"[warning] Received non-JSON message: {raw_message}")
            return None

    def _handle_stream_start(self, data: Dict[str, Any]) -> None:
        self._state.stream_started = True
        self._state.stream_started_at = self._now()
        self._tool_tracker.reset()
        self._state.tool_call_durations.clear()
        message = data.get("message") or "Stream started"
        print(f"[stream_start] {message}")

    def _handle_stream_chunk(self, data: Dict[str, Any]) -> None:
        chunk = data.get("message", "")
        if chunk is None:
            return
        if not isinstance(chunk, str):
            chunk = str(chunk)
        timestamp = self._now()
        if self._state.first_chunk_at is None:
            self._state.first_chunk_at = timestamp
        self._state.last_chunk_at = timestamp
        self._state.chunks.append(chunk)
        sys.stdout.write(chunk)
        sys.stdout.flush()
        self._last_chunk_ended_with_newline = chunk.endswith("\n")

    def _handle_stream_end(self, data: Dict[str, Any]) -> None:
        self._state.stream_completed = True
        self._state.stream_completed_at = self._now()
        full_response = data.get("full_response")
        if isinstance(full_response, str) and full_response:
            self._state.full_response = full_response
        elif not self._state.full_response:
            self._state.full_response = "".join(self._state.chunks)

        if not self._last_chunk_ended_with_newline:
            sys.stdout.write("\n")
            sys.stdout.flush()

        finish_message = data.get("message") or "Stream completed"
        print(f"[stream_end] {finish_message}")

        if self._state.full_response:
            print(f"[summary] Full response length: {len(self._state.full_response)} characters")

        if self._state.tool_events:
            counts = Counter(event.get("type", "unknown") for event in self._state.tool_events)
            summary = ", ".join(f"{key}={value}" for key, value in counts.items())
            print(f"[summary] Tool events observed: {summary}")
        if self._state.tool_call_durations:
            avg_duration = sum(self._state.tool_call_durations) / len(self._state.tool_call_durations)
            longest = max(self._state.tool_call_durations)
            print(f"[summary] Tool durations avg={avg_duration:.2f}s max={longest:.2f}s")

        if self._workflow_tracker:
            for message in self._workflow_tracker.finalize():
                print(message)

        self._report_metrics_and_validation()

    def _handle_tool_stream(self, payload: Any, envelope_type: str) -> None:
        if not isinstance(payload, dict):
            self._log_line(envelope_type, payload)
            return

        client_time = self._now()
        if self._state.first_tool_event_at is None:
            self._state.first_tool_event_at = client_time
        self._state.last_tool_event_at = client_time

        event_type = str(payload.get("type", "") or "unknown")
        normalized_type = event_type.lower()
        raw_tool_name = (
            payload.get("tool_name")
            or payload.get("tool")
            or payload.get("name")
            or "unknown_tool"
        )
        tool_name = str(raw_tool_name)

        duration: Optional[float] = None
        if normalized_type in {"tool_call_start", "tool_start"}:
            self._tool_tracker.record_start(tool_name, client_time)
        elif normalized_type in {"tool_call_result", "tool_result", "tool_call_error", "tool_error"}:
            duration = self._tool_tracker.record_finish(tool_name, client_time)
            if duration is not None:
                self._state.tool_call_durations.append(duration)

        event_copy: Dict[str, Any] = dict(payload)
        event_copy.setdefault("tool_name", tool_name)
        event_copy["client_timestamp"] = client_time
        if self._state.stream_started_at is not None:
            event_copy["client_offset"] = client_time - self._state.stream_started_at
        if duration is not None:
            event_copy["client_duration"] = duration

        self._state.tool_events.append(event_copy)
        formatted = self._format_tool_event(event_copy, envelope_type)
        print(formatted)

        if self._workflow_tracker:
            for message in self._workflow_tracker.record_event(tool_name, event_type):
                print(message)

    def _report_metrics_and_validation(self) -> None:
        metrics: List[str] = []
        state = self._state

        if state.stream_started_at is not None and state.first_chunk_at is not None:
            metrics.append(f"first_chunk={state.first_chunk_at - state.stream_started_at:.2f}s")
        if state.first_chunk_at is not None and state.last_chunk_at is not None and state.last_chunk_at != state.first_chunk_at:
            metrics.append(f"chunk_span={state.last_chunk_at - state.first_chunk_at:.2f}s")
        if state.first_tool_event_at is not None:
            if state.stream_started_at is not None:
                metrics.append(f"first_tool={state.first_tool_event_at - state.stream_started_at:.2f}s")
            if state.last_tool_event_at is not None and state.last_tool_event_at != state.first_tool_event_at:
                metrics.append(f"tool_span={state.last_tool_event_at - state.first_tool_event_at:.2f}s")
        if state.stream_started_at is not None and state.stream_completed_at is not None:
            metrics.append(f"total={state.stream_completed_at - state.stream_started_at:.2f}s")
        if state.tool_call_durations:
            avg_duration = sum(state.tool_call_durations) / len(state.tool_call_durations)
            metrics.append(f"tool_avg={avg_duration:.2f}s")
            metrics.append(f"tool_max={max(state.tool_call_durations):.2f}s")

        if metrics:
            print(f"[metrics] {' | '.join(metrics)}")

        if state.message_counts:
            counts = ", ".join(f"{key}:{value}" for key, value in sorted(state.message_counts.items()))
            print(f"[counts] {counts}")

        if not state.stream_started and self._validation.min_chunks > 0:
            print("[validation] stream_start message missing")
            self._validation_failed = True

        chunk_count = len(state.chunks)
        if self._validation.min_chunks > 0:
            if chunk_count >= self._validation.min_chunks:
                print(f"[validation] chunk_count OK ({chunk_count})")
            else:
                print(
                    "[validation] chunk_count FAIL "
                    f"(expected ≥ {self._validation.min_chunks}, got {chunk_count})"
                )
                self._validation_failed = True
        elif chunk_count:
            print(f"[validation] chunk_count observed ({chunk_count})")

        tool_count = len(state.tool_events)
        if self._validation.min_tool_events > 0:
            if tool_count >= self._validation.min_tool_events:
                print(f"[validation] tool_events OK ({tool_count})")
            else:
                print(
                    "[validation] tool_events FAIL "
                    f"(expected ≥ {self._validation.min_tool_events}, got {tool_count})"
                )
                self._validation_failed = True
        elif tool_count:
            print(f"[validation] tool_events observed ({tool_count})")

    def _format_tool_event(self, payload: Dict[str, Any], envelope_type: str) -> str:
        event_type = payload.get("type", "unknown")
        tool_name = payload.get("tool_name") or payload.get("tool") or payload.get("name") or "unknown_tool"
        parts = [f"[{envelope_type}] {tool_name} {event_type}"]

        offset = payload.get("client_offset")
        if isinstance(offset, (int, float)):
            parts.append(f"t+{offset:.2f}s")

        status = payload.get("status")
        if status:
            parts.append(f"status={status}")

        duration = payload.get("client_duration")
        if isinstance(duration, (int, float)):
            parts.append(f"duration={duration:.2f}s")

        if event_type == "tool_call_start":
            args = payload.get("args") or payload.get("arguments")
            if args:
                parts.append(f"args={self._short_json(args)}")
        elif event_type == "tool_call_result":
            result = payload.get("result")
            if result is not None:
                parts.append(f"result={self._short_json(result)}")
        elif event_type in {"tool_message", "tool_call_message"}:
            message = payload.get("message") or payload.get("content")
            if message:
                parts.append(f"message={self._short_text(message)}")
        elif event_type == "tool_call_error":
            error = payload.get("error") or payload.get("message")
            if error:
                parts.append(f"error={error}")
        else:
            extras = {
                key: value
                for key, value in payload.items()
                if key not in {
                    "type",
                    "tool_name",
                    "tool",
                    "name",
                    "args",
                    "arguments",
                    "result",
                    "message",
                    "content",
                    "error",
                    "status",
                    "client_timestamp",
                    "client_offset",
                    "client_duration",
                }
            }
            if extras:
                parts.append(f"data={self._short_json(extras)}")

        return " ".join(parts)

    @staticmethod
    def _short_json(value: Any, limit: int = 200) -> str:
        try:
            text = json.dumps(value, ensure_ascii=True)
        except TypeError:
            text = str(value)
        if len(text) > limit:
            return f"{text[:limit]}... (truncated)"
        return text

    @staticmethod
    def _short_text(value: Any, limit: int = 200) -> str:
        text = str(value)
        if len(text) > limit:
            return f"{text[:limit]}... (truncated)"
        return text

    @staticmethod
    def _log_line(prefix: str, content: Any) -> None:
        if content is None:
            return
        if isinstance(content, (dict, list)):
            formatted = json.dumps(content, ensure_ascii=True, indent=2)
        else:
            formatted = str(content)
        print(f"[{prefix}] {formatted}")

    def _flag_non_streaming(self, reason: str) -> None:
        if self._validation.min_chunks > 0:
            print(f"[validation] Expected streaming output but received '{reason}' instead")
            self._validation_failed = True


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="MCP WebSocket streaming client for shortage analyzer validation"
    )
    parser.add_argument("--host", default="localhost", help="FastAPI server host (default: localhost)")
    parser.add_argument("--port", type=int, default=8000, help="FastAPI server port (default: 8000)")
    parser.add_argument(
        "--endpoint",
        choices=["storage", "mysql", "alert", "alert_manager", "shortage_workflow"],
        default="alert",
        help=(
            "Target WebSocket endpoint (storage = shortage analyzer, alert = alert manager, "
            "mysql = database agent; alert_manager is accepted as an alias; "
            "shortage_workflow triggers the orchestrated MySQL → Shortage → Alert sequence)"
        ),
    )
    parser.add_argument(
        "--user-id",
        default="demo_alert_client",
        help="User identifier for the session",
    )
    parser.add_argument(
        "--message",
        default=None,
        help="Custom message to send after connecting (overrides default for selected endpoint)",
    )
    parser.add_argument(
        "--sql",
        default=DEFAULT_SQL_MESSAGE,
        help="SQL message to send when using the mysql endpoint (default: SHOW TABLES;)",
    )
    parser.add_argument(
        "--query-param",
        default=None,
        help="Optional SQL query to embed in the connection URL when using mysql endpoint",
    )
    parser.add_argument(
        "--company",
        default="Tech Pioneer Co., Ltd.",
        help="Company name query parameter when using the storage endpoint",
    )
    parser.add_argument(
        "--timeout",
        type=float,
        default=120.0,
        help="Seconds to wait for each server message before timing out (default: 120)",
    )
    parser.add_argument(
        "--expect-chunks",
        type=int,
        default=1,
        help="Minimum stream_chunk messages required before considering the run successful (default: 1)",
    )
    parser.add_argument(
        "--expect-tool-events",
        type=int,
        default=1,
        help="Minimum tool stream events required before considering the run successful (default: 1)",
    )
    return parser.parse_args()


def main() -> None:
    args = parse_args()

    normalized_endpoint = MCPStreamingClient.normalize_endpoint(args.endpoint)
    workflow_stages: Optional[Sequence[Tuple[str, Sequence[str]]]] = None

    if args.endpoint == "shortage_workflow":
        normalized_endpoint = "orchestration"
        message = args.message or DEFAULT_SHORTAGE_WORKFLOW_QUERY
        query_params = {
            "company": args.company,
            "mode": "pattern_based",
            "workflow": "mysql-shortage-alert",
        }
        workflow_stages = SHORTAGE_WORKFLOW_STAGES
    elif normalized_endpoint == "mysql":
        message = args.message or args.sql or DEFAULT_SQL_MESSAGE
        query_value = args.query_param if args.query_param is not None else message
        query_params = {"query": query_value}
    elif normalized_endpoint == "alert":
        message = args.message or DEFAULT_ALERT_MESSAGE
        query_params = {"company": args.company}
    else:
        message = args.message or DEFAULT_SHORTAGE_MESSAGE
        query_params = {"company": args.company}

    min_chunks = max(0, args.expect_chunks)
    min_tool_events = max(0, args.expect_tool_events)
    if workflow_stages:
        min_tool_events = max(min_tool_events, len(workflow_stages))

    validation = ValidationConfig(
        min_chunks=min_chunks,
        min_tool_events=min_tool_events,
    )

    client = MCPStreamingClient(
        host=args.host,
        port=args.port,
        user_id=args.user_id,
        endpoint=args.endpoint,
        query_params=query_params,
        message=message,
        timeout=args.timeout,
        validation=validation,
        workflow_stages=workflow_stages,
    )

    try:
        asyncio.run(client.run())
    except KeyboardInterrupt:
        print("\n[interrupt] Client stopped by user")
    else:
        if client.validation_failed:
            sys.exit(1)


if __name__ == "__main__":
    main()
