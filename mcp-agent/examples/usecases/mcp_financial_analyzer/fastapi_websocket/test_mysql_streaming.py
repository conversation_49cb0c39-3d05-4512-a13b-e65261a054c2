#!/usr/bin/env python3
"""
Test script for MySQL agent streaming functionality.
Tests the integration of the streaming MySQL agent with WebSocket endpoints.
"""

import asyncio
import websockets
import json
import sys
from datetime import datetime

async def test_mysql_streaming():
    """Test MySQL agent streaming integration."""
    print("🧪 Testing MySQL Agent Streaming Integration")
    print("=" * 60)
    
    uri = "ws://localhost:8000/ws/mysql/test_user?query=SHOW TABLES;"
    
    try:
        print(f"📡 Connecting to: {uri}")
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connection established")
            
            # Wait for welcome message
            welcome = await websocket.recv()
            welcome_data = json.loads(welcome)
            print(f"📨 Welcome: {welcome_data}")
            
            # Test messages
            test_messages = [
                "SHOW TABLES;",
                "DESCRIBE CUSTOMER_ORDERS;", 
                "SELECT COUNT(*) FROM CUSTOMER_ORDERS LIMIT 5;"
            ]
            
            for i, message in enumerate(test_messages, 1):
                print(f"\n🔍 Test {i}: Sending MySQL query")
                print(f"Query: {message}")
                
                # Send message with streaming enabled
                await websocket.send(json.dumps({
                    "message": message,
                    "streaming": True
                }))
                print("📤 Message sent, waiting for streaming response...")
                
                # Collect streaming response
                stream_started = False
                stream_content = ""
                
                while True:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                        data = json.loads(response)
                        
                        if data["type"] == "stream_start":
                            print("🚀 Stream started")
                            stream_started = True
                            
                        elif data["type"] == "stream_chunk":
                            stream_content += data["message"]
                            print(f"📦 Chunk: {data['message'][:50]}{'...' if len(data['message']) > 50 else ''}")
                            
                        elif data["type"] == "stream_end":
                            print("🏁 Stream ended")
                            print(f"📋 Complete response length: {len(stream_content)} characters")
                            print(f"📝 Response preview: {stream_content[:200]}{'...' if len(stream_content) > 200 else ''}")
                            break
                            
                        elif data["type"] == "mcp_tool_stream":
                            print(f"🔧 Tool stream: {data.get('data', {}).get('tool_name', 'unknown')} - {data.get('data', {}).get('status', 'unknown')}")
                            
                        elif data["type"] == "error":
                            print(f"❌ Error: {data['message']}")
                            break
                            
                        else:
                            print(f"📨 Other message: {data['type']} - {data.get('message', '')[:100]}")
                            
                    except asyncio.TimeoutError:
                        print("⏰ Timeout waiting for response")
                        break
                    except Exception as e:
                        print(f"❌ Error receiving message: {e}")
                        break
                
                if stream_started and stream_content:
                    print(f"✅ Test {i} completed successfully")
                else:
                    print(f"⚠️ Test {i} may have issues - no streaming content received")
                
                # Small delay between tests
                await asyncio.sleep(1)
            
            print(f"\n🎉 MySQL streaming integration test completed!")
            print(f"📊 Tests run: {len(test_messages)}")
            print(f"⏰ Timestamp: {datetime.now().isoformat()}")
            
    except websockets.exceptions.ConnectionRefused:
        print("❌ Connection refused. Is the FastAPI server running on port 8000?")
        print("💡 Start the server with: python main.py")
        return False
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🧪 MySQL Agent Streaming Integration Test")
    print("🔗 Ensure the FastAPI WebSocket server is running on localhost:8000")
    print("🗄️ Ensure MySQL MCP server is available at configured endpoint")
    print("-" * 60)
    
    try:
        success = asyncio.run(test_mysql_streaming())
        if success:
            print("\n✅ All tests passed! MySQL streaming integration is working.")
            sys.exit(0)
        else:
            print("\n❌ Tests failed. Check server configuration and logs.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user.")
        sys.exit(1)