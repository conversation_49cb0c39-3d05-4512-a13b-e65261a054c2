{"workflow_id": "workflow_d568f084", "original_query": "Check inventory for CUSTORD-********* and calculate shortage risk", "query_type": "shortage_analysis", "workflow_pattern": "mysql_shortage", "mysql_context": {"query": "Check inventory for CUSTORD-********* and calculate shortage risk", "response": "", "reasoning": "", "table_data": null, "entities_found": {}, "success": false, "execution_time": 0.0020000003278255463, "error": "'BaseAgentWrapper' object has no attribute 'memory'"}, "shortage_context": {"company_name": "Unknown", "shortage_index": 0.6, "risk_level": "MEDIUM", "weighted_shortage_index": null, "components_analyzed": {}, "recommendations": [], "response": "Analysis for Unknown encountered an unexpected error. Please contact support.", "mcp_service_status": "unknown", "calculation_method": "fallback", "success": true, "execution_time": 0.00299999862909317, "error": null}, "alert_context": null, "metadata": {"created_at": "2025-09-15T08:48:01.021607", "updated_at": "2025-09-15T08:48:01.027669", "version": "1.0", "workflow_id": "workflow_d568f084", "query_hash": "00e2ea7b", "agent_executions": ["mysql:2025-09-15T08:48:01.024022", "shortage:2025-09-15T08:48:01.027670"]}, "current_step": 2, "total_steps": 3, "is_complete": false, "errors": ["Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"], "warnings": []}