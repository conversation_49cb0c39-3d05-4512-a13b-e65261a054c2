{"workflow_id": "workflow_24a186d7", "original_query": "Analyze shortage for order CUSTORD-********* and send customer alerts", "query_type": "customer_priority", "workflow_pattern": "full_workflow", "mysql_context": {"query": "Analyze shortage for order CUSTORD-********* and send customer alerts", "response": "", "reasoning": "", "table_data": null, "entities_found": {}, "success": false, "execution_time": 0.0, "error": "'BaseAgentWrapper' object has no attribute 'memory'"}, "shortage_context": {"company_name": "Unknown", "shortage_index": 0.6, "risk_level": "MEDIUM", "weighted_shortage_index": null, "components_analyzed": {}, "recommendations": [], "response": "Analysis for Unknown encountered an unexpected error. Please contact support.", "mcp_service_status": "unknown", "calculation_method": "fallback", "success": true, "execution_time": 0.0020000003278255463, "error": null}, "alert_context": null, "metadata": {"created_at": "2025-09-11T09:12:25.749674", "updated_at": "2025-09-11T09:12:25.752781", "version": "1.0", "workflow_id": "workflow_24a186d7", "query_hash": "13da5cb1", "agent_executions": ["mysql:2025-09-11T09:12:25.750210", "shortage:2025-09-11T09:12:25.752784"]}, "current_step": 2, "total_steps": 3, "is_complete": false, "errors": ["Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"], "warnings": []}