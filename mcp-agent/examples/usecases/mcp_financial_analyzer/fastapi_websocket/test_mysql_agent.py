#!/usr/bin/env python3
"""
Test MySQL agent creation to debug the initialization issue
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import asyncio
from agents.mysql_agent import create_mysql_orchestrator_agent

async def test_mysql_agent_creation():
    """Test creating the MySQL agent"""
    print("🔍 Testing MySQL agent creation...")
    
    try:
        agent = create_mysql_orchestrator_agent()
        print("✅ MySQL agent created successfully!")
        
        # Try to enter the agent context
        await agent.__aenter__()
        print("✅ MySQL agent context entered successfully!")
        
        # Try to exit the agent context
        await agent.__aexit__(None, None, None)
        print("✅ MySQL agent context exited successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ MySQL agent creation failed: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return False

async def main():
    success = await test_mysql_agent_creation()
    if success:
        print("\n🎉 MySQL agent test passed!")
    else:
        print("\n💥 MySQL agent test failed!")

if __name__ == "__main__":
    asyncio.run(main())