{"test_execution_time": "2025-09-05T11:24:00.121564", "total_tests": 11, "successful_tests": 0, "failed_tests": 11, "success_rate": 0.0, "agent_performance": {}, "overall_performance": {}, "test_details": [{"endpoint": "error_test_basic_table_query_streaming", "success": false, "errors": ["False is not true : mysql_basic_query failed: ['Message ordering validation failed', 'Streaming completeness validation failed', 'Expected >= 5 LLM chunks, got 2']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_data_analysis_streaming", "success": false, "errors": ["False is not true : mysql_data_analysis failed: ['Failed to connect to /ws/mysql']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_performance_analysis_streaming", "success": false, "errors": ["False is not true : mysql_performance_analysis failed: ['Failed to connect to /ws/mysql']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_basic_shortage_analysis_streaming", "success": false, "errors": ["False is not true : storage_analyzer_basic failed: ['Message ordering validation failed']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_complex_multi_component_streaming", "success": false, "errors": ["False is not true : storage_analyzer_complex failed: ['Message ordering validation failed']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_supply_chain_stress_streaming", "success": false, "errors": ["False is not true : storage_analyzer_stress failed: ['Message ordering validation failed', 'Streaming completeness validation failed', 'Expected >= 10 LLM chunks, got 2']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_alert_aggregation_workflow_streaming", "success": false, "errors": ["False is not true : alert_manager_workflow failed: ['Message ordering validation failed']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_critical_system_alert_streaming", "success": false, "errors": ["False is not true : alert_manager_critical failed: ['Message ordering validation failed']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_multi_channel_notification_streaming", "success": false, "errors": ["False is not true : alert_manager_multichannel failed: ['Message ordering validation failed']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_concurrent_agent_streaming", "success": false, "errors": ["65.48312568664551 not less than 60.0 : Concurrent execution took 65.48s, expected < 60.0s"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_latency_benchmarks", "success": false, "errors": ["44.25794863700867 not less than 25.0 : alert_manager total duration exceeded threshold"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}]}