#!/usr/bin/env python3
"""
Test script for the comprehensive WebSocket client.

This script validates that the client can be imported and basic functionality works.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from financial_websocket_client import FinancialWebSocketClient
    print("✅ Successfully imported FinancialWebSocketClient")
except ImportError as e:
    print(f"❌ Failed to import FinancialWebSocketClient: {e}")
    sys.exit(1)


async def test_client_initialization():
    """Test that the client can be initialized properly."""
    try:
        client = FinancialWebSocketClient()
        print(f"✅ Client initialized successfully")
        print(f"   User ID: {client.user_id}")
        print(f"   Base URL: {client.base_url}")
        print(f"   Demo scenarios: {len(client.demo_scenarios)} available")
        
        # Test banner and scenario display
        print("\n--- Testing Banner ---")
        client.print_banner()
        
        print("\n--- Testing Demo Scenarios Display ---")
        client.print_demo_scenarios()
        
        # Test message formatting
        print("\n--- Testing Message Formatting ---")
        test_messages = [
            ("system", "System message test"),
            ("stream_start", "Stream starting"),
            ("stream_chunk", "Stream chunk data"),
            ("stream_end", "Stream completed"),
            ("error", "Error message test"),
            ("success", "Success message test")
        ]
        
        for msg_type, content in test_messages:
            formatted = client.format_message(msg_type, content)
            print(formatted)
        
        return True
        
    except Exception as e:
        print(f"❌ Client initialization failed: {e}")
        return False


async def test_connection_handling():
    """Test connection handling (without actually connecting)."""
    try:
        client = FinancialWebSocketClient()
        
        # Test that connection fails gracefully when server is not running
        print("\n--- Testing Connection Handling ---")
        print("Testing connection to non-existent server (should fail gracefully)...")
        
        success = await client.connect_websocket("/ws/demo/test_user?scenario=1")
        if not success:
            print("✅ Connection failure handled gracefully")
        else:
            print("⚠️ Unexpected connection success")
            await client.disconnect_websocket()
        
        return True
        
    except Exception as e:
        print(f"❌ Connection handling test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🧪 Testing Comprehensive WebSocket Client")
    print("=" * 50)
    
    tests = [
        ("Client Initialization", test_client_initialization),
        ("Connection Handling", test_connection_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 30)
        
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            print(f"❌ {test_name}: FAILED with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The client is ready to use.")
        print("\nTo use the client:")
        print("  python financial_websocket_client.py                    # Interactive mode")
        print("  python financial_websocket_client.py --demo 1          # Run demo scenario 1")
        print("  python financial_websocket_client.py --demo-all        # Run all demos")
        print("  python financial_websocket_client.py --help            # Show all options")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite failed with exception: {e}")
        sys.exit(1)
