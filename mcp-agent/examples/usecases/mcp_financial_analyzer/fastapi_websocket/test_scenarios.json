{"mysql_agent_scenarios": [{"name": "basic_table_query", "description": "Test basic SQL query with schema tools", "endpoint": "/ws/mysql", "connection_params": {"query": "SHOW TABLES"}, "test_message": "Show me all the tables in the database and describe their structure", "expected_tool_calls": 1, "expected_llm_chunks": 3, "expected_tools": ["mysql_query", "schema_inspector"], "validation_criteria": {"min_response_length": 100, "should_contain": ["table", "structure", "schema"], "max_duration": 30.0, "max_chunk_latency": 2.0}}, {"name": "data_analysis_query", "description": "Test data retrieval with analysis", "endpoint": "/ws/mysql", "connection_params": {"query": "SELECT COUNT(*) FROM users WHERE active = 1"}, "test_message": "Count all active users and provide insights about user engagement", "expected_tool_calls": 1, "expected_llm_chunks": 3, "expected_tools": ["mysql_query"], "validation_criteria": {"min_response_length": 80, "should_contain": ["users", "active", "count"], "max_duration": 25.0, "max_chunk_latency": 1.5}}, {"name": "performance_analysis", "description": "Test performance analysis query", "endpoint": "/ws/mysql", "connection_params": {"query": "EXPLAIN SELECT * FROM orders WHERE date > '2024-01-01'"}, "test_message": "Analyze the performance of this query and suggest optimizations", "expected_tool_calls": 1, "expected_llm_chunks": 3, "expected_tools": ["mysql_query", "performance_analyzer"], "validation_criteria": {"min_response_length": 150, "should_contain": ["performance", "query", "optimization"], "max_duration": 35.0, "max_chunk_latency": 2.0}}], "storage_analyzer_scenarios": [{"name": "basic_shortage_analysis", "description": "Test basic component shortage analysis", "endpoint": "/ws/storage", "connection_params": {"company": "TechCorp"}, "test_message": "Analyze shortage risk for CPUs: Intel Core i7-12700K (200 units needed), AMD Ryzen 7 5800X (150 units needed), current inventory: Intel 50 units, AMD 30 units", "expected_tool_calls": 1, "expected_llm_chunks": 3, "expected_tools": ["ShortageIndex", "WeightedShortageIndex"], "validation_criteria": {"min_response_length": 200, "should_contain": ["shortage", "risk", "CPU", "analysis"], "max_duration": 40.0, "max_chunk_latency": 2.5}}, {"name": "complex_multi_component_analysis", "description": "Test complex multi-component shortage analysis", "endpoint": "/ws/storage", "connection_params": {"company": "ManufacturingCorp"}, "test_message": "Analyze shortage for complex system: GPUs RTX 4090 (100 needed, 10 available), Motherboards ASUS Z790 (100 needed, 25 available), RAM DDR5-5600 (200 needed, 150 available), SSDs Samsung 980 PRO (100 needed, 80 available). Provide detailed risk assessment.", "expected_tool_calls": 1, "expected_llm_chunks": 3, "expected_tools": ["ShortageIndex", "WeightedShortageIndex", "ComponentAnalyzer"], "validation_criteria": {"min_response_length": 300, "should_contain": ["GPU", "motherboard", "RAM", "SSD", "risk"], "max_duration": 50.0, "max_chunk_latency": 3.0}}, {"name": "supply_chain_stress_test", "description": "Test supply chain stress scenario", "endpoint": "/ws/storage", "connection_params": {"company": "GlobalTech"}, "test_message": "Critical shortage scenario: All semiconductor components facing 80% shortage due to supply chain disruption. Analyze immediate risks and mitigation strategies for production lines.", "expected_tool_calls": 1, "expected_llm_chunks": 3, "expected_tools": ["ShortageIndex", "WeightedShortageIndex", "RiskAnalyzer", "MitigationP<PERSON>ner"], "validation_criteria": {"min_response_length": 400, "should_contain": ["critical", "shortage", "supply chain", "mitigation"], "max_duration": 60.0, "max_chunk_latency": 3.5}}], "alert_manager_scenarios": [{"name": "critical_system_alert", "description": "Test critical system alert processing", "endpoint": "/ws/alert", "connection_params": {"company": "DataCenter Inc"}, "test_message": "CRITICAL: Database server cluster failure detected. 3 nodes down, automatic failover initiated. Immediate attention required for production systems.", "expected_tool_calls": 3, "expected_llm_chunks": 5, "expected_tools": ["alert_processor", "notification_sender", "escalation_manager"], "validation_criteria": {"min_response_length": 150, "should_contain": ["critical", "database", "failover", "alert"], "max_duration": 30.0, "max_chunk_latency": 2.0}}, {"name": "multi_channel_notification", "description": "Test multi-channel alert notification", "endpoint": "/ws/alert", "connection_params": {"company": "E-commerce Platform"}, "test_message": "High-priority security alert: Suspicious login attempts detected from multiple IPs. Send notifications via email, SMS, and Slack to security team and on-call engineers.", "expected_tool_calls": 4, "expected_llm_chunks": 6, "expected_tools": ["alert_processor", "email_notifier", "sms_notifier", "slack_notifier"], "validation_criteria": {"min_response_length": 200, "should_contain": ["security", "notification", "email", "SMS", "<PERSON><PERSON>ck"], "max_duration": 35.0, "max_chunk_latency": 2.5}}, {"name": "alert_aggregation_workflow", "description": "Test alert aggregation and workflow", "endpoint": "/ws/alert", "connection_params": {"company": "CloudServices Ltd"}, "test_message": "Multiple related alerts: API latency spike (10 alerts), increased error rates (5 alerts), database connection issues (3 alerts). Aggregate and create incident workflow with proper stakeholder notifications.", "expected_tool_calls": 1, "expected_llm_chunks": 3, "expected_tools": ["alert_aggregator", "incident_creator", "workflow_manager", "notification_dispatcher", "escalation_tracker"], "validation_criteria": {"min_response_length": 250, "should_contain": ["aggregation", "incident", "workflow", "stakeholder"], "max_duration": 45.0, "max_chunk_latency": 3.0}}], "concurrent_test_scenarios": [{"name": "multi_agent_concurrent", "description": "Test all three agents concurrently", "test_cases": [{"agent": "mysql", "endpoint": "/ws/mysql", "connection_params": {"query": "SELECT COUNT(*) FROM active_sessions"}, "test_message": "Count current active database sessions"}, {"agent": "storage", "endpoint": "/ws/storage", "connection_params": {"company": "ConcurrentTest"}, "test_message": "Quick shortage check: RAM shortage analysis for 100 DDR4 modules"}, {"agent": "alert", "endpoint": "/ws/alert", "connection_params": {"company": "ConcurrentTest"}, "test_message": "Process medium priority alert: System resource utilization above 85%"}], "validation_criteria": {"max_concurrent_duration": 60.0, "min_success_rate": 0.8, "max_cross_contamination": 0.0}}], "error_handling_scenarios": [{"name": "connection_timeout", "description": "Test connection timeout handling", "test_type": "connection_error", "scenarios": [{"endpoint": "/ws/mysql", "user_id": "timeout_test", "connection_params": {"query": "SELECT SLEEP(120)"}, "expected_behavior": "graceful_timeout"}]}, {"name": "invalid_message_format", "description": "Test invalid message format handling", "test_type": "message_error", "scenarios": [{"endpoint": "/ws/storage", "user_id": "format_test", "connection_params": {"company": "TestCorp"}, "invalid_message": "{'invalid': 'json format'", "expected_behavior": "error_response"}]}, {"name": "tool_failure_recovery", "description": "Test tool failure recovery", "test_type": "tool_error", "scenarios": [{"endpoint": "/ws/alert", "user_id": "tool_failure_test", "connection_params": {"company": "FailureTest"}, "test_message": "Trigger tool that should fail gracefully", "expected_behavior": "fallback_response"}]}], "performance_benchmarks": {"latency_thresholds": {"mysql_agent": {"max_first_response": 3.0, "max_chunk_latency": 1.5, "max_total_duration": 30.0}, "storage_analyzer": {"max_first_response": 5.0, "max_chunk_latency": 2.5, "max_total_duration": 50.0}, "alert_manager": {"max_first_response": 2.0, "max_chunk_latency": 1.5, "max_total_duration": 25.0}}, "throughput_targets": {"concurrent_sessions": 10, "messages_per_second": 50, "success_rate": 0.95}}}