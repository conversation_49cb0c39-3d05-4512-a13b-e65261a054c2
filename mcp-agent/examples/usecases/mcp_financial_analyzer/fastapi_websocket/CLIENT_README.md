# Financial Analysis Orchestration WebSocket Client

A comprehensive WebSocket client that demonstrates the newly integrated orchestration functionality in the Financial Analysis WebSocket server. This client provides both interactive and programmatic interfaces for testing demo scenarios, enhanced orchestration, and real-time streaming capabilities.

## Features

### 🎭 Demo Scenarios
Four predefined demo scenarios that showcase the complete orchestration pipeline:

1. **Critical Database Shortage Analysis with Customer Alert**
   - Triggers: MySQL → Shortage Analysis → Alert Manager pipeline
   - Flow: Database query → Shortage calculation → Customer notification

2. **Supplier Risk Alert with SLA Breach Notification**
   - Tests: Database → analysis → customer notification workflow
   - Flow: Supplier inventory check → Risk analysis → SLA breach alerts

3. **Urgent Customer Priority Alert Analysis**
   - Flow: Priority order check → Critical shortage detection → Customer escalation

4. **Multi-Order Critical Alert Orchestration**
   - Full pipeline: Multi-order analysis → Weighted shortage indices → Comprehensive alerting

### 🎼 Enhanced Orchestration
- **Pattern-based execution**: Optimized for streaming with predefined workflow patterns
- **Orchestrator-based execution**: Advanced reasoning with dynamic workflow generation
- **Custom query support**: Execute any financial analysis query with real-time streaming
- **Execution mode selection**: Choose between different orchestration approaches

### 🖥️ Interactive Features
- **Command-line interface**: User-friendly menu system for easy navigation
- **Real-time streaming display**: Live progress updates with colored output
- **Error handling**: Graceful handling of connection failures and server errors
- **Comprehensive logging**: Detailed logs for debugging and analysis

## Installation

### Prerequisites
```bash
# Required packages (install if not available)
pip install websockets asyncio
```

### Optional Dependencies
```bash
# For colored output (recommended)
pip install colorama
```

## Usage

### Interactive Mode (Default)
```bash
python financial_websocket_client.py
```

This launches an interactive menu where you can:
- Select and run demo scenarios
- Execute custom orchestration queries
- View available demo scenarios
- Change user ID
- Navigate through options easily

### Command Line Options

#### Run Specific Demo Scenario
```bash
# Run demo scenario 1
python financial_websocket_client.py --demo 1

# Run demo scenario 2
python financial_websocket_client.py --demo 2
```

#### Run All Demo Scenarios
```bash
python financial_websocket_client.py --demo-all
```

#### Enhanced Orchestration with Custom Query
```bash
# Pattern-based execution (default)
python financial_websocket_client.py --orchestration "Check inventory for CUSTORD-202506001"

# Orchestrator-based execution
python financial_websocket_client.py --orchestration "Analyze supplier risks for MetaMind Technology" --execution-mode orchestrator_based
```

#### Custom Server URL
```bash
python financial_websocket_client.py --url ws://*************:8000
```

#### Custom User ID
```bash
python financial_websocket_client.py --user-id my_custom_user
```

#### Non-Interactive Mode (for scripting)
```bash
python financial_websocket_client.py --demo 1 --no-interactive
```

### Help
```bash
python financial_websocket_client.py --help
```

## Example Output

### Demo Scenario Execution
```
🎭 Running Demo Scenario 1
============================================================
Name: Critical Database Shortage Analysis with Customer Alert
Description: Triggers MySQL → Shortage Analysis → Alert Manager pipeline
Expected Flow: Database query → Shortage calculation → Customer notification
============================================================

[10:10:39] ℹ️ INFO: Connecting to: ws://localhost:8000/ws/demo/client_ea18f199?scenario=1
[10:10:39] ✅ SUCCESS: Connected successfully to /ws/demo/client_ea18f199?scenario=1
[10:10:39] ℹ️ INFO: Listening for messages... (Press Ctrl+C to stop)
[10:10:56] 🔧 SYSTEM: Welcome to Demo Scenario 1! Preparing to execute...
[10:10:56] 🚀 STREAM_START: Starting demo scenario 1...
[10:10:56] 📡 STREAM_CHUNK: --- Demo Scenario 1: CRITICAL Database Shortage Analysis ---
[10:10:56] 📡 STREAM_CHUNK: Result: ✓ SUCCESS
[10:10:56] 📡 STREAM_CHUNK: Execution Time: 17.32s
[10:10:56] 📡 STREAM_CHUNK: Workflow ID: workflow_5789ea0b
[10:10:56] ✅ STREAM_END: Demo scenario complete
[10:10:56] ✅ SUCCESS: Stream completed

============================================================
📊 Demo Scenario 1 Results
============================================================
Messages Received: 15
✅ Demo scenario 1 completed successfully!
```

## WebSocket Endpoints

The client connects to these WebSocket endpoints:

### Demo Scenarios
- `ws://localhost:8000/ws/demo/{user_id}?scenario={1-4}`

### Enhanced Orchestration
- `ws://localhost:8000/ws/orchestration/{user_id}?mode={execution_mode}`

## Message Types

The client handles various WebSocket message types:

- **system**: System messages and notifications
- **stream_start**: Beginning of streaming operation
- **stream_chunk**: Real-time progress updates
- **stream_end**: Completion of streaming operation
- **error**: Error messages and failures
- **success**: Success confirmations

## Error Handling

The client includes comprehensive error handling:

- **Connection failures**: Graceful handling when server is unavailable
- **Message parsing errors**: Robust JSON parsing with fallbacks
- **WebSocket disconnections**: Automatic cleanup and status reporting
- **Keyboard interrupts**: Clean shutdown on Ctrl+C
- **Server errors**: Display and logging of server-side errors

## Logging

The client creates detailed logs:

- **Console output**: Real-time colored output for user interaction
- **Log files**: Timestamped log files for debugging (`websocket_client_YYYYMMDD_HHMMSS.log`)
- **Structured logging**: Comprehensive logging with levels and context

## Testing

Run the test suite to validate client functionality:

```bash
python test_client.py
```

This validates:
- Client initialization
- Message formatting
- Connection handling
- Error scenarios

## Configuration

### Default Settings
- **Server URL**: `ws://localhost:8000`
- **User ID**: Auto-generated (`client_xxxxxxxx`)
- **Execution Mode**: `pattern_based`
- **Colors**: Enabled (if colorama is available)

### Customization
All settings can be customized via command-line arguments or by modifying the client initialization.

## Integration with WebSocket Server

This client is designed to work with the enhanced Financial Analysis WebSocket server that includes:

- Orchestration functionality integration
- Demo scenario endpoints
- Enhanced orchestration with execution modes
- Real-time streaming capabilities
- Comprehensive error handling

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure the WebSocket server is running on the specified URL
   - Check firewall settings and network connectivity

2. **Import Errors**
   - Install required dependencies: `pip install websockets`
   - For colored output: `pip install colorama`

3. **No Response from Server**
   - Verify the server is properly initialized with orchestration support
   - Check server logs for any initialization errors

4. **Demo Scenarios Not Working**
   - Ensure MCP servers are running (MySQL: 8702, Shortage: 6970, Alert: 6972)
   - Check orchestration system health via server endpoints

## Advanced Usage

### Scripting
The client can be used in scripts with the `--no-interactive` flag:

```bash
#!/bin/bash
# Run all demo scenarios and capture results
python financial_websocket_client.py --demo-all --no-interactive > results.log 2>&1
```

### Custom Integration
The `FinancialWebSocketClient` class can be imported and used in other Python applications:

```python
from financial_websocket_client import FinancialWebSocketClient

async def custom_test():
    client = FinancialWebSocketClient("ws://localhost:8000")
    result = await client.run_demo_scenario(1)
    print(f"Demo result: {result}")
```

## Support

For issues or questions:
1. Check the server logs for orchestration system status
2. Run the test suite to validate client functionality
3. Review the comprehensive logging output
4. Ensure all MCP servers are running and healthy
