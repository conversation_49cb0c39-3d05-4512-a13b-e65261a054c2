{"test_execution_time": "2025-09-05T08:58:03.225868", "total_tests": 12, "successful_tests": 0, "failed_tests": 12, "success_rate": 0.0, "agent_performance": {}, "overall_performance": {}, "test_details": [{"endpoint": "error_test_basic_table_query_streaming", "success": false, "errors": ["False is not true : mysql_basic_query failed: ['Failed to connect to /ws/mysql']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_data_analysis_streaming", "success": false, "errors": ["False is not true : mysql_data_analysis failed: ['Failed to connect to /ws/mysql']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_performance_analysis_streaming", "success": false, "errors": ["False is not true : mysql_performance_analysis failed: ['Failed to connect to /ws/mysql']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_basic_shortage_analysis_streaming", "success": false, "errors": ["False is not true : storage_analyzer_basic failed: ['Expected >= 2 tool calls, got 0', 'Expected >= 6 LLM chunks, got 4']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_complex_multi_component_streaming", "success": false, "errors": ["False is not true : storage_analyzer_complex failed: ['Expected >= 3 tool calls, got 0', 'Expected >= 8 LLM chunks, got 4']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_supply_chain_stress_streaming", "success": false, "errors": ["False is not true : storage_analyzer_stress failed: ['Expected >= 4 tool calls, got 0', 'Expected >= 10 LLM chunks, got 4']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_alert_aggregation_workflow_streaming", "success": false, "errors": ["False is not true : alert_manager_workflow failed: ['Failed to connect to /ws/alert']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_critical_system_alert_streaming", "success": false, "errors": ["False is not true : alert_manager_critical failed: ['Failed to connect to /ws/alert']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_multi_channel_notification_streaming", "success": false, "errors": ["False is not true : alert_manager_multichannel failed: ['Failed to connect to /ws/alert']"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "error_test_concurrent_agent_streaming", "success": false, "errors": ["0.0 not greater than or equal to 0.8 : Success rate 0.00 below threshold 0.8"], "performance": {"total_duration": 0, "average_chunk_latency": 0}, "message_details": {"total": 0, "tool_calls": 0, "llm_chunks": 0, "errors": 1}}, {"endpoint": "/ws/storage", "success": false, "errors": ["Expected >= 1 tool calls, got 0"], "performance": {"total_duration": 2.271596908569336, "total_messages": 5, "messages_per_second": 2.2010947369835203, "average_chunk_latency": 2.27154335975647, "tool_call_ratio": 0.0, "llm_stream_ratio": 0.8, "error_ratio": 0.0}, "message_details": {"total": 5, "tool_calls": 0, "llm_chunks": 4, "errors": 0}, "validation_timestamp": "2025-09-05T08:58:01.751222"}, {"endpoint": "/ws/alert", "success": false, "errors": ["Expected >= 1 tool calls, got 0"], "performance": {"total_duration": 1.433039903640747, "total_messages": 5, "messages_per_second": 3.4890863731687576, "average_chunk_latency": 1.4329875469207765, "tool_call_ratio": 0.0, "llm_stream_ratio": 0.8, "error_ratio": 0.0}, "message_details": {"total": 5, "tool_calls": 0, "llm_chunks": 4, "errors": 0}, "validation_timestamp": "2025-09-05T08:58:03.225846"}]}