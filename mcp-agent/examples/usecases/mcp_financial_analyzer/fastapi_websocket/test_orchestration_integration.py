#!/usr/bin/env python3
"""
Test script for the orchestration integration in the WebSocket server.

This script validates that the orchestration functionality has been properly
integrated into the FastAPI WebSocket server.
"""

import asyncio
import json
import logging
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from session_manager import FinancialSessionManager, SessionType, setup_comprehensive_logging


async def test_session_manager_initialization():
    """Test that the session manager initializes with orchestration support."""
    print("=" * 60)
    print("Testing Session Manager Initialization")
    print("=" * 60)
    
    # Setup logging
    try:
        debug_log_file, llm_debug_file = setup_comprehensive_logging(show_llm_realtime=False)
        print(f"✓ Comprehensive logging initialized")
        print(f"  Debug log: {debug_log_file}")
        print(f"  LLM log: {llm_debug_file}")
    except Exception as e:
        print(f"⚠️ Logging setup failed: {e}")
    
    # Initialize session manager
    session_manager = FinancialSessionManager()
    
    try:
        print("Initializing session manager...")
        await session_manager.initialize()
        
        # Check if orchestration runner is available
        if session_manager.orchestration_runner:
            print("✓ Orchestration runner initialized successfully")
            
            # Test health check
            try:
                health = await session_manager.run_orchestration_health_check()
                print(f"✓ Health check completed: {health.get('status', 'unknown')}")
                
                # Display component status
                components = health.get('components', {})
                for component, status in components.items():
                    if isinstance(status, dict):
                        comp_status = status.get('status', 'unknown')
                        print(f"  - {component}: {comp_status}")
                    else:
                        print(f"  - {component}: {status}")
                        
            except Exception as e:
                print(f"⚠️ Health check failed: {e}")
                
        else:
            print("⚠️ Orchestration runner not available - this is expected if MCP servers are not running")
            
        # Test other orchestration methods
        try:
            stats = session_manager.get_orchestration_statistics()
            print(f"✓ Statistics retrieved: {stats}")
        except Exception as e:
            print(f"⚠️ Statistics retrieval failed: {e}")
            
        try:
            query_types = session_manager.get_supported_query_types()
            print(f"✓ Supported query types: {len(query_types)} types")
        except Exception as e:
            print(f"⚠️ Query types retrieval failed: {e}")
            
        try:
            patterns = session_manager.get_available_patterns()
            print(f"✓ Available patterns: {len(patterns)} patterns")
        except Exception as e:
            print(f"⚠️ Patterns retrieval failed: {e}")
            
    except Exception as e:
        print(f"✗ Session manager initialization failed: {e}")
        return False
    
    finally:
        # Cleanup
        try:
            await session_manager.cleanup()
            print("✓ Session manager cleanup completed")
        except Exception as e:
            print(f"⚠️ Cleanup failed: {e}")
    
    return True


async def test_orchestration_session_creation():
    """Test creating an orchestration session."""
    print("\n" + "=" * 60)
    print("Testing Orchestration Session Creation")
    print("=" * 60)
    
    session_manager = FinancialSessionManager()
    
    try:
        await session_manager.initialize()
        
        # Create an orchestration session
        user_id = "test_user_123"
        session_type = SessionType.ORCHESTRATION
        company_name = "Test Company Inc."
        
        print(f"Creating orchestration session for user: {user_id}")
        session = await session_manager.get_or_create_session(user_id, session_type, company_name)
        
        print(f"✓ Session created successfully")
        print(f"  Session ID: {session.session_id}")
        print(f"  User ID: {session.user_id}")
        print(f"  Session Type: {session.session_type.value}")
        print(f"  Company: {session.company_name}")
        
        # Test setting execution mode
        session.execution_mode = "pattern_based"
        print(f"✓ Execution mode set: {session.execution_mode}")
        
        # Test session cleanup
        await session_manager.cleanup_session(user_id, session_type)
        print("✓ Session cleanup completed")
        
    except Exception as e:
        print(f"✗ Session creation failed: {e}")
        return False
    
    finally:
        try:
            await session_manager.cleanup()
        except Exception as e:
            print(f"⚠️ Final cleanup failed: {e}")
    
    return True


async def test_demo_scenario_availability():
    """Test that demo scenarios are available."""
    print("\n" + "=" * 60)
    print("Testing Demo Scenario Availability")
    print("=" * 60)
    
    session_manager = FinancialSessionManager()
    
    try:
        await session_manager.initialize()
        
        # Test demo scenario execution (without actually running if no MCP servers)
        if session_manager.orchestration_runner:
            print("✓ Orchestration runner available - demo scenarios can be executed")
            
            # Test demo scenario 1 (this will fail if MCP servers aren't running, but that's expected)
            try:
                print("Testing demo scenario 1 execution...")
                
                # Create a simple stream callback for testing
                messages = []
                async def test_stream_callback(message):
                    messages.append(message)
                    print(f"  Stream: {message.strip()}")
                
                result = await session_manager.execute_demo_scenario(1, test_stream_callback)
                print(f"✓ Demo scenario 1 executed: {result.get('success', False)}")
                print(f"  Messages received: {len(messages)}")
                
            except Exception as e:
                print(f"⚠️ Demo scenario execution failed (expected if MCP servers not running): {e}")
                
        else:
            print("⚠️ Orchestration runner not available - demo scenarios cannot be executed")
            print("  This is expected if MCP servers are not running")
            
    except Exception as e:
        print(f"✗ Demo scenario test failed: {e}")
        return False
    
    finally:
        try:
            await session_manager.cleanup()
        except Exception as e:
            print(f"⚠️ Final cleanup failed: {e}")
    
    return True


async def main():
    """Run all integration tests."""
    print("🧪 Orchestration Integration Test Suite")
    print("=" * 60)
    print("This test validates the integration of orchestration functionality")
    print("into the FastAPI WebSocket server.")
    print("=" * 60)
    
    tests = [
        ("Session Manager Initialization", test_session_manager_initialization),
        ("Orchestration Session Creation", test_orchestration_session_creation),
        ("Demo Scenario Availability", test_demo_scenario_availability),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Results Summary")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Orchestration integration is working correctly.")
    else:
        print("⚠️ Some tests failed. This may be expected if MCP servers are not running.")
        print("   The integration code is in place and should work when servers are available.")
    
    return passed == total


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite failed with exception: {e}")
        sys.exit(1)
