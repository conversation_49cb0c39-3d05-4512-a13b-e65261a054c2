#!/usr/bin/env python3
"""
Streaming WebSocket Test Client for MCP Agent Validation

This module provides utilities for testing WebSocket streaming functionality
for tool calls and LLM responses in the MCP Financial Analyzer system.
"""

import asyncio
import json
import time
import websockets
from datetime import datetime
from typing import List, Dict, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging
from urllib.parse import urlencode, quote_plus

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """WebSocket message types for streaming validation."""
    STREAM_START = "stream_start"
    STREAM_CHUNK = "stream_chunk"
    STREAM_END = "stream_end"
    TOOL_START = "tool_start"
    TOOL_PROGRESS = "tool_progress"  
    TOOL_RESULT = "tool_result"
    SYSTEM = "system"
    ERROR = "error"


@dataclass
class StreamingMessage:
    """Represents a captured streaming message."""
    timestamp: float
    message_type: str
    content: str
    user_id: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def from_websocket_message(cls, message: Dict[str, Any]) -> 'StreamingMessage':
        """Create StreamingMessage from WebSocket message."""
        return cls(
            timestamp=time.time(),
            message_type=message.get("type", "unknown"),
            content=message.get("message", ""),
            user_id=message.get("user_id", ""),
            metadata={k: v for k, v in message.items() 
                     if k not in ["type", "message", "user_id"]}
        )


@dataclass 
class StreamingValidationResult:
    """Results from streaming validation test."""
    success: bool
    total_messages: int
    tool_call_messages: int
    llm_stream_messages: int
    error_messages: int
    total_duration: float
    average_chunk_latency: float
    messages: List[StreamingMessage] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    
    def add_error(self, error: str):
        """Add error to validation result."""
        self.errors.append(error)
        self.success = False


class StreamingWebSocketClient:
    """WebSocket client for testing streaming functionality."""
    
    def __init__(self, base_url: str = "ws://localhost:8000"):
        self.base_url = base_url
        self.messages: List[StreamingMessage] = []
        self.connected = False
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        
    async def connect(self, endpoint: str, user_id: str, **params) -> bool:
        """Connect to WebSocket endpoint with parameters."""
        try:
            # Build URL with query parameters
            # Ensure query parameter values are URL-encoded to avoid handshake 400 errors
            safe_params = {}
            for key, value in params.items():
                # Convert all values to str for consistent encoding
                safe_params[str(key)] = str(value)
            query_params = urlencode(safe_params, quote_via=quote_plus)
            url = f"{self.base_url}{endpoint}/{user_id}"
            if query_params:
                url += f"?{query_params}"
                
            logger.info(f"Connecting to {url}")
            self.websocket = await websockets.connect(url)
            self.connected = True
            logger.info(f"Connected successfully to {url}")
            return True
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
            
    async def disconnect(self):
        """Disconnect from WebSocket."""
        if self.websocket:
            await self.websocket.close()
            self.connected = False
            logger.info("Disconnected from WebSocket")
            
    async def send_message(self, message: str, streaming: bool = True) -> StreamingValidationResult:
        """Send message and capture streaming response."""
        if not self.connected or not self.websocket:
            raise RuntimeError("Not connected to WebSocket")
            
        self.messages.clear()
        start_time = time.time()
        
        # Send message
        request = {
            "message": message,
            "streaming": streaming
        }
        
        logger.info(f"Sending message: {message[:100]}...")
        await self.websocket.send(json.dumps(request))
        
        # Capture streaming response
        tool_call_count = 0
        llm_stream_count = 0
        error_count = 0
        chunk_latencies = []
        
        try:
            async for response in self.websocket:
                try:
                    data = json.loads(response)
                    msg = StreamingMessage.from_websocket_message(data)
                    self.messages.append(msg)
                    
                    # Calculate chunk latency
                    chunk_latency = msg.timestamp - start_time
                    chunk_latencies.append(chunk_latency)
                    
                    # Count message types (including MCP tool stream wrapper)
                    if msg.message_type in [MessageType.TOOL_START.value,
                                            MessageType.TOOL_PROGRESS.value,
                                            MessageType.TOOL_RESULT.value]:
                        tool_call_count += 1
                    elif msg.message_type in ["mcp_tool_stream", "tool_stream"]:
                        # Interpret nested MCP tool stream events
                        nested = msg.metadata.get("data", {}) if isinstance(msg.metadata, dict) else {}
                        nested_type = str(nested.get("type", "")).lower()
                        if any(t in nested_type for t in ["tool_call_start", "tool_start"]):
                            tool_call_count += 1
                        elif any(t in nested_type for t in ["tool_call_progress", "tool_progress"]):
                            tool_call_count += 1
                        elif any(t in nested_type for t in ["tool_call_result", "tool_result"]):
                            tool_call_count += 1
                        elif any(t in nested_type for t in ["tool_call_error", "tool_error"]):
                            error_count += 1
                    elif msg.message_type in [MessageType.STREAM_START.value,
                                            MessageType.STREAM_CHUNK.value,
                                            MessageType.STREAM_END.value]:
                        llm_stream_count += 1
                    elif msg.message_type == MessageType.ERROR.value:
                        error_count += 1
                    
                    # Stop on stream end or error
                    if msg.message_type in [MessageType.STREAM_END.value, MessageType.ERROR.value]:
                        break
                        
                except json.JSONDecodeError as e:
                    logger.error(f"JSON decode error: {e}")
                    error_count += 1
                    
        except Exception as e:
            logger.error(f"Error receiving messages: {e}")
            error_count += 1
            
        # Calculate metrics
        end_time = time.time()
        total_duration = end_time - start_time
        avg_latency = sum(chunk_latencies) / len(chunk_latencies) if chunk_latencies else 0
        
        return StreamingValidationResult(
            success=(error_count == 0 and len(self.messages) > 0),
            total_messages=len(self.messages),
            tool_call_messages=tool_call_count,
            llm_stream_messages=llm_stream_count,
            error_messages=error_count,
            total_duration=total_duration,
            average_chunk_latency=avg_latency,
            messages=self.messages.copy()
        )
        
    def validate_message_ordering(self, result: StreamingValidationResult) -> bool:
        """Validate that tool calls complete before LLM streaming starts."""
        tool_call_start_times = []
        tool_call_result_times = []
        llm_content_times = []
        
        for msg in result.messages:
            # Handle MCP tool stream wrapper messages
            if msg.message_type == "mcp_tool_stream" and isinstance(msg.metadata, dict):
                data = msg.metadata.get("data", {})
                if isinstance(data, dict):
                    nested_type = data.get("type", "")
                    if nested_type == "tool_call_start":
                        tool_call_start_times.append(msg.timestamp)
                    elif nested_type == "tool_call_result":
                        tool_call_result_times.append(msg.timestamp)
            
            # Handle direct tool messages (legacy format)
            elif msg.message_type.startswith("tool_"):
                if "start" in msg.message_type:
                    tool_call_start_times.append(msg.timestamp)
                elif "result" in msg.message_type:
                    tool_call_result_times.append(msg.timestamp)
            
            # Handle LLM stream chunks (ignore stream_start, focus on actual content)
            elif msg.message_type == "stream_chunk":
                # Only count non-empty content chunks
                if msg.content and msg.content.strip() and msg.content.strip() != "...":
                    llm_content_times.append(msg.timestamp)
                
        # More lenient ordering validation:
        # 1. If we have tool results, they should complete before substantial LLM content
        # 2. Allow some overlap for performance reasons
        if tool_call_result_times and llm_content_times:
            # Use the latest tool result completion time
            last_tool_result = max(tool_call_result_times)
            # Use the first substantial LLM content (skip empty/placeholder chunks)
            first_substantial_llm = min(llm_content_times) if llm_content_times else float('inf')
            
            # Allow 0.1 second tolerance for near-simultaneous streaming
            tolerance = 0.1
            return (last_tool_result <= first_substantial_llm + tolerance)
        
        # If no tool results or no LLM content, consider ordering valid
        return True
        
    def validate_streaming_completeness(self, result: StreamingValidationResult) -> bool:
        """Validate that streaming has proper start/end messages."""
        stream_starts = [msg for msg in result.messages 
                        if msg.message_type == MessageType.STREAM_START.value]
        stream_ends = [msg for msg in result.messages 
                      if msg.message_type == MessageType.STREAM_END.value]
        
        # Should have matching start/end pairs
        return len(stream_starts) == len(stream_ends)
        
    def get_performance_metrics(self, result: StreamingValidationResult) -> Dict[str, Any]:
        """Extract performance metrics from validation result."""
        return {
            "total_duration": result.total_duration,
            "total_messages": result.total_messages,
            "messages_per_second": result.total_messages / result.total_duration if result.total_duration > 0 else 0,
            "average_chunk_latency": result.average_chunk_latency,
            "tool_call_ratio": result.tool_call_messages / result.total_messages if result.total_messages > 0 else 0,
            "llm_stream_ratio": result.llm_stream_messages / result.total_messages if result.total_messages > 0 else 0,
            "error_ratio": result.error_messages / result.total_messages if result.total_messages > 0 else 0
        }


class AgentStreamingValidator:
    """High-level validator for agent streaming functionality."""
    
    def __init__(self, base_url: str = "ws://localhost:8000"):
        self.client = StreamingWebSocketClient(base_url)
        
    async def validate_agent_streaming(
        self, 
        endpoint: str,
        user_id: str, 
        test_message: str,
        expected_tool_calls: int = 1,
        expected_llm_chunks: int = 5,
        **connection_params
    ) -> StreamingValidationResult:
        """Validate streaming functionality for a specific agent."""
        
        # Connect to agent endpoint
        connected = await self.client.connect(endpoint, user_id, **connection_params)
        if not connected:
            result = StreamingValidationResult(
                success=False, total_messages=0, tool_call_messages=0,
                llm_stream_messages=0, error_messages=1, 
                total_duration=0, average_chunk_latency=0
            )
            result.add_error(f"Failed to connect to {endpoint}")
            return result
            
        try:
            # Send test message and validate response
            result = await self.client.send_message(test_message)
            
            # Additional validations
            if not self.client.validate_message_ordering(result):
                result.add_error("Message ordering validation failed")
                
            if not self.client.validate_streaming_completeness(result):
                result.add_error("Streaming completeness validation failed")
                
            # Validate expected message counts
            if result.tool_call_messages < expected_tool_calls:
                result.add_error(f"Expected >= {expected_tool_calls} tool calls, got {result.tool_call_messages}")
                
            if result.llm_stream_messages < expected_llm_chunks:
                result.add_error(f"Expected >= {expected_llm_chunks} LLM chunks, got {result.llm_stream_messages}")
                
            return result
            
        finally:
            await self.client.disconnect()


async def test_endpoint_streaming(
    endpoint: str,
    user_id: str,
    test_message: str,
    connection_params: Dict[str, Any] = None,
    expected_tools: int = 1,
    expected_chunks: int = 5
) -> Dict[str, Any]:
    """Test streaming functionality for a single endpoint."""
    
    connection_params = connection_params or {}
    validator = AgentStreamingValidator()
    
    logger.info(f"Testing {endpoint} with message: {test_message[:50]}...")
    
    result = await validator.validate_agent_streaming(
        endpoint=endpoint,
        user_id=user_id,
        test_message=test_message,
        expected_tool_calls=expected_tools,
        expected_llm_chunks=expected_chunks,
        **connection_params
    )
    
    # Compile test report
    performance = validator.client.get_performance_metrics(result)
    
    return {
        "endpoint": endpoint,
        "success": result.success,
        "errors": result.errors,
        "performance": performance,
        "message_details": {
            "total": result.total_messages,
            "tool_calls": result.tool_call_messages,
            "llm_chunks": result.llm_stream_messages,
            "errors": result.error_messages
        },
        "validation_timestamp": datetime.now().isoformat()
    }


if __name__ == "__main__":
    # Example usage
    async def main():
        # Test MySQL agent streaming
        result = await test_endpoint_streaming(
            endpoint="/ws/mysql",
            user_id="test_user",
            test_message="SHOW TABLES",
            connection_params={"query": "SHOW TABLES"},
            expected_tools=1,
            expected_chunks=3
        )
        
        print(json.dumps(result, indent=2))
        
    asyncio.run(main())