#!/usr/bin/env python3
"""
Performance comparison test between orchestration streaming and MySQL agent streaming.

This test validates that the orchestrated shortage workflow delivers streaming performance
that matches or exceeds the individual MySQL agent endpoint behavior.

Usage:
    python test_streaming_performance_comparison.py
"""

import asyncio
import json
import logging
import time
import websockets
from datetime import datetime
from typing import Dict, List, Any, Tuple
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StreamingPerformanceComparator:
    """Compares streaming performance between orchestration and MySQL agent endpoints."""
    
    def __init__(self, server_url: str = "ws://localhost:8000"):
        self.server_url = server_url
        self.comparison_results = {
            "test_start_time": datetime.now().isoformat(),
            "mysql_agent_results": {},
            "orchestration_results": {},
            "performance_comparison": {},
            "success": False
        }
        
    async def test_mysql_agent_streaming(self, user_id: str = "mysql_test_user") -> Dict[str, Any]:
        """Test MySQL agent streaming performance."""
        logger.info("Testing MySQL agent streaming performance")
        
        test_message = {
            "message": "Query database for CUSTORD-202506001 component requirements and availability",
            "streaming": True
        }
        
        mysql_url = f"{self.server_url}/ws/mysql/{user_id}"
        
        return await self._test_endpoint_streaming(mysql_url, test_message, "mysql_agent")
        
    async def test_orchestration_streaming(self, user_id: str = "orchestration_test_user") -> Dict[str, Any]:
        """Test orchestration streaming performance."""
        logger.info("Testing orchestration streaming performance")
        
        test_message = {
            "message": "CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION.",
            "streaming": True
        }
        
        orchestration_url = f"{self.server_url}/ws/orchestration/{user_id}?mode=pattern_based"
        
        return await self._test_endpoint_streaming(orchestration_url, test_message, "orchestration")
        
    async def _test_endpoint_streaming(self, url: str, message: Dict[str, Any], endpoint_type: str) -> Dict[str, Any]:
        """Test streaming performance for a specific endpoint."""
        results = {
            "endpoint_type": endpoint_type,
            "url": url,
            "start_time": time.time(),
            "streaming_metrics": {},
            "events": [],
            "errors": []
        }
        
        try:
            logger.info(f"Connecting to {endpoint_type} endpoint: {url}")
            async with websockets.connect(url) as websocket:
                logger.info(f"✓ {endpoint_type} WebSocket connection established")
                
                # Track streaming metrics
                stream_start_time = None
                first_chunk_time = None
                last_chunk_time = None
                chunk_count = 0
                tool_event_count = 0
                total_text_length = 0
                
                # Send test message
                logger.info(f"Sending test message to {endpoint_type}")
                await websocket.send(json.dumps(message))
                
                # Collect streaming events
                while True:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=45.0)
                        event_time = time.time()
                        data = json.loads(response)
                        event_type = data.get("type", "unknown")
                        
                        results["events"].append({
                            "timestamp": event_time,
                            "type": event_type,
                            "data": data
                        })
                        
                        if event_type == "stream_start":
                            stream_start_time = event_time
                            logger.info(f"🚀 {endpoint_type} stream started")
                            
                        elif event_type == "stream_chunk":
                            chunk_count += 1
                            if first_chunk_time is None:
                                first_chunk_time = event_time
                            last_chunk_time = event_time
                            
                            chunk_text = data.get("message", "")
                            total_text_length += len(chunk_text)
                            
                            logger.info(f"📦 {endpoint_type} chunk #{chunk_count}: {len(chunk_text)} chars")
                            
                        elif event_type in ["mcp_tool_stream", "tool_stream"]:
                            tool_event_count += 1
                            tool_data = data.get("data", {})
                            tool_name = tool_data.get("tool_name", "unknown")
                            tool_status = tool_data.get("type", "unknown")
                            
                            logger.info(f"🔧 {endpoint_type} tool event #{tool_event_count}: {tool_name} - {tool_status}")
                            
                        elif event_type == "stream_end":
                            logger.info(f"🏁 {endpoint_type} stream ended")
                            break
                            
                        elif event_type == "error":
                            error_msg = data.get("message", "Unknown error")
                            logger.error(f"❌ {endpoint_type} error: {error_msg}")
                            results["errors"].append({
                                "timestamp": event_time,
                                "error": error_msg
                            })
                            break
                            
                    except asyncio.TimeoutError:
                        logger.error(f"❌ {endpoint_type} timeout waiting for response")
                        results["errors"].append({
                            "timestamp": time.time(),
                            "error": "Timeout waiting for response"
                        })
                        break
                        
                # Calculate streaming metrics
                if stream_start_time and first_chunk_time:
                    time_to_first_chunk = first_chunk_time - stream_start_time
                    total_stream_time = last_chunk_time - stream_start_time if last_chunk_time else 0
                    
                    results["streaming_metrics"] = {
                        "time_to_first_chunk_seconds": time_to_first_chunk,
                        "total_stream_time_seconds": total_stream_time,
                        "total_chunks": chunk_count,
                        "total_tool_events": tool_event_count,
                        "total_text_length": total_text_length,
                        "chunks_per_second": chunk_count / total_stream_time if total_stream_time > 0 else 0,
                        "tool_events_per_second": tool_event_count / total_stream_time if total_stream_time > 0 else 0,
                        "chars_per_second": total_text_length / total_stream_time if total_stream_time > 0 else 0,
                        "average_chunk_size": total_text_length / chunk_count if chunk_count > 0 else 0
                    }
                    
                results["end_time"] = time.time()
                results["total_test_time"] = results["end_time"] - results["start_time"]
                
                logger.info(f"✓ {endpoint_type} test completed successfully")
                
        except Exception as e:
            logger.error(f"❌ {endpoint_type} connection error: {e}")
            results["errors"].append({
                "timestamp": time.time(),
                "error": f"Connection error: {str(e)}"
            })
            
        return results
        
    async def run_comparison_test(self) -> Dict[str, Any]:
        """Run comparison test between MySQL agent and orchestration streaming."""
        logger.info("Starting streaming performance comparison test")
        
        # Test MySQL agent streaming
        mysql_results = await self.test_mysql_agent_streaming()
        self.comparison_results["mysql_agent_results"] = mysql_results
        
        # Wait a moment between tests
        await asyncio.sleep(2)
        
        # Test orchestration streaming
        orchestration_results = await self.test_orchestration_streaming()
        self.comparison_results["orchestration_results"] = orchestration_results
        
        # Compare performance
        self.comparison_results["performance_comparison"] = self._compare_performance(
            mysql_results, orchestration_results
        )
        
        # Evaluate overall success
        self.comparison_results["success"] = self._evaluate_comparison()
        self.comparison_results["test_end_time"] = datetime.now().isoformat()
        
        return self.comparison_results
        
    def _compare_performance(self, mysql_results: Dict[str, Any], orchestration_results: Dict[str, Any]) -> Dict[str, Any]:
        """Compare performance metrics between MySQL and orchestration endpoints."""
        mysql_metrics = mysql_results.get("streaming_metrics", {})
        orchestration_metrics = orchestration_results.get("streaming_metrics", {})
        
        comparison = {
            "metrics_comparison": {},
            "performance_ratios": {},
            "winner": {},
            "analysis": []
        }
        
        # Compare key metrics
        metrics_to_compare = [
            "time_to_first_chunk_seconds",
            "total_stream_time_seconds", 
            "total_chunks",
            "total_tool_events",
            "chunks_per_second",
            "tool_events_per_second",
            "chars_per_second"
        ]
        
        for metric in metrics_to_compare:
            mysql_value = mysql_metrics.get(metric, 0)
            orchestration_value = orchestration_metrics.get(metric, 0)
            
            comparison["metrics_comparison"][metric] = {
                "mysql_agent": mysql_value,
                "orchestration": orchestration_value,
                "difference": orchestration_value - mysql_value,
                "percentage_difference": ((orchestration_value - mysql_value) / mysql_value * 100) if mysql_value > 0 else 0
            }
            
            # Calculate performance ratios (orchestration / mysql)
            if mysql_value > 0:
                comparison["performance_ratios"][metric] = orchestration_value / mysql_value
            else:
                comparison["performance_ratios"][metric] = float('inf') if orchestration_value > 0 else 1
                
            # Determine winner for each metric
            if metric in ["time_to_first_chunk_seconds", "total_stream_time_seconds"]:
                # Lower is better for timing metrics
                winner = "mysql_agent" if mysql_value < orchestration_value else "orchestration"
            else:
                # Higher is better for throughput metrics
                winner = "mysql_agent" if mysql_value > orchestration_value else "orchestration"
                
            comparison["winner"][metric] = winner
            
        # Generate analysis
        analysis = []
        
        # Time to first chunk analysis
        ttfc_ratio = comparison["performance_ratios"].get("time_to_first_chunk_seconds", 1)
        if ttfc_ratio <= 1.2:  # Within 20%
            analysis.append("✓ Orchestration time-to-first-chunk is competitive with MySQL agent")
        else:
            analysis.append("⚠️ Orchestration time-to-first-chunk is slower than MySQL agent")
            
        # Throughput analysis
        chunks_ratio = comparison["performance_ratios"].get("chunks_per_second", 1)
        if chunks_ratio >= 0.8:  # At least 80% of MySQL performance
            analysis.append("✓ Orchestration chunk throughput is competitive with MySQL agent")
        else:
            analysis.append("⚠️ Orchestration chunk throughput is significantly lower than MySQL agent")
            
        # Tool events analysis
        tool_ratio = comparison["performance_ratios"].get("tool_events_per_second", 1)
        if orchestration_metrics.get("total_tool_events", 0) > mysql_metrics.get("total_tool_events", 0):
            analysis.append("✓ Orchestration provides more tool events (expected for multi-agent workflow)")
        else:
            analysis.append("⚠️ Orchestration provides fewer tool events than expected")
            
        comparison["analysis"] = analysis
        
        return comparison
        
    def _evaluate_comparison(self) -> bool:
        """Evaluate if the comparison test passed."""
        mysql_results = self.comparison_results["mysql_agent_results"]
        orchestration_results = self.comparison_results["orchestration_results"]
        comparison = self.comparison_results["performance_comparison"]
        
        # Check for errors
        if mysql_results.get("errors") or orchestration_results.get("errors"):
            logger.error("❌ Errors encountered during testing")
            return False
            
        # Check if both endpoints provided streaming
        mysql_metrics = mysql_results.get("streaming_metrics", {})
        orchestration_metrics = orchestration_results.get("streaming_metrics", {})
        
        if not mysql_metrics or not orchestration_metrics:
            logger.error("❌ Missing streaming metrics")
            return False
            
        # Performance criteria
        criteria = {
            "max_ttfc_ratio": 2.0,      # Orchestration TTFC should be within 2x of MySQL
            "min_chunks_ratio": 0.5,    # Orchestration should have at least 50% chunk throughput
            "min_tool_events": 3,       # Orchestration should have at least 3 tool events
            "min_chunks": 5             # Both should have at least 5 chunks
        }
        
        results = []
        
        # Check time to first chunk ratio
        ttfc_ratio = comparison["performance_ratios"].get("time_to_first_chunk_seconds", float('inf'))
        if ttfc_ratio <= criteria["max_ttfc_ratio"]:
            results.append(True)
            logger.info(f"✓ Time to first chunk ratio: {ttfc_ratio:.2f} (≤ {criteria['max_ttfc_ratio']})")
        else:
            results.append(False)
            logger.error(f"❌ Time to first chunk ratio too high: {ttfc_ratio:.2f}")
            
        # Check chunk throughput ratio
        chunks_ratio = comparison["performance_ratios"].get("chunks_per_second", 0)
        if chunks_ratio >= criteria["min_chunks_ratio"]:
            results.append(True)
            logger.info(f"✓ Chunks per second ratio: {chunks_ratio:.2f} (≥ {criteria['min_chunks_ratio']})")
        else:
            results.append(False)
            logger.error(f"❌ Chunks per second ratio too low: {chunks_ratio:.2f}")
            
        # Check orchestration tool events
        orchestration_tool_events = orchestration_metrics.get("total_tool_events", 0)
        if orchestration_tool_events >= criteria["min_tool_events"]:
            results.append(True)
            logger.info(f"✓ Orchestration tool events: {orchestration_tool_events} (≥ {criteria['min_tool_events']})")
        else:
            results.append(False)
            logger.error(f"❌ Insufficient orchestration tool events: {orchestration_tool_events}")
            
        # Check minimum chunks for both
        mysql_chunks = mysql_metrics.get("total_chunks", 0)
        orchestration_chunks = orchestration_metrics.get("total_chunks", 0)
        if mysql_chunks >= criteria["min_chunks"] and orchestration_chunks >= criteria["min_chunks"]:
            results.append(True)
            logger.info(f"✓ Sufficient chunks: MySQL={mysql_chunks}, Orchestration={orchestration_chunks}")
        else:
            results.append(False)
            logger.error(f"❌ Insufficient chunks: MySQL={mysql_chunks}, Orchestration={orchestration_chunks}")
            
        return all(results)
        
    def generate_comparison_report(self) -> str:
        """Generate a comprehensive comparison report."""
        report = []
        report.append("=" * 80)
        report.append("STREAMING PERFORMANCE COMPARISON REPORT")
        report.append("=" * 80)
        report.append(f"Test Start: {self.comparison_results['test_start_time']}")
        report.append(f"Test End: {self.comparison_results.get('test_end_time', 'N/A')}")
        report.append(f"Overall Success: {'✓ PASS' if self.comparison_results['success'] else '❌ FAIL'}")
        report.append("")
        
        # Performance comparison
        comparison = self.comparison_results.get("performance_comparison", {})
        if comparison:
            report.append("PERFORMANCE METRICS COMPARISON:")
            report.append("-" * 50)
            
            metrics_comparison = comparison.get("metrics_comparison", {})
            for metric, data in metrics_comparison.items():
                mysql_val = data["mysql_agent"]
                orch_val = data["orchestration"]
                diff_pct = data["percentage_difference"]
                winner = comparison["winner"].get(metric, "tie")
                
                report.append(f"{metric}:")
                report.append(f"  MySQL Agent: {mysql_val:.3f}")
                report.append(f"  Orchestration: {orch_val:.3f}")
                report.append(f"  Difference: {diff_pct:+.1f}% (Winner: {winner})")
                report.append("")
                
            # Analysis
            analysis = comparison.get("analysis", [])
            if analysis:
                report.append("PERFORMANCE ANALYSIS:")
                report.append("-" * 30)
                for point in analysis:
                    report.append(f"  {point}")
                report.append("")
        
        return "\n".join(report)


async def main():
    """Main test execution function."""
    print("🧪 Starting Streaming Performance Comparison Test")
    print("=" * 60)
    
    comparator = StreamingPerformanceComparator()
    
    try:
        # Run the comparison test
        results = await comparator.run_comparison_test()
        
        # Generate and display report
        report = comparator.generate_comparison_report()
        print(report)
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"streaming_performance_comparison_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"📄 Detailed results saved to: {results_file}")
        
        # Exit with appropriate code
        exit_code = 0 if results["success"] else 1
        print(f"\n🏁 Test completed with exit code: {exit_code}")
        return exit_code
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        print(f"\n❌ Test execution failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
