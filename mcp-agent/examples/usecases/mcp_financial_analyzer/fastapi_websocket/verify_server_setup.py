#!/usr/bin/env python3
"""
Quick verification script to check if the server is running and endpoints are accessible.

This script performs basic connectivity tests before running the comprehensive streaming tests.

Usage:
    python verify_server_setup.py [--server-url ws://localhost:8000]
"""

import asyncio
import argparse
import json
import logging
import websockets
from datetime import datetime
import sys
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ServerSetupVerifier:
    """Verifies server setup and endpoint accessibility."""
    
    def __init__(self, server_url: str = "ws://localhost:8000"):
        self.server_url = server_url
        self.verification_results = {
            "server_url": server_url,
            "test_time": datetime.now().isoformat(),
            "endpoints": {},
            "overall_status": False
        }
        
    async def verify_endpoint(self, endpoint_path: str, test_message: dict = None) -> dict:
        """Verify a specific WebSocket endpoint."""
        url = f"{self.server_url}{endpoint_path}"
        
        if test_message is None:
            test_message = {"message": "Hello, testing connection", "streaming": False}
            
        result = {
            "url": url,
            "accessible": False,
            "response_received": False,
            "error": None,
            "response_time_seconds": None
        }
        
        try:
            logger.info(f"Testing endpoint: {url}")
            start_time = asyncio.get_event_loop().time()
            
            async with websockets.connect(url) as websocket:
                result["accessible"] = True
                logger.info(f"✓ Connected to {endpoint_path}")
                
                # Send test message
                await websocket.send(json.dumps(test_message))
                
                # Wait for response (with timeout)
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    end_time = asyncio.get_event_loop().time()
                    result["response_time_seconds"] = end_time - start_time
                    result["response_received"] = True
                    
                    # Try to parse response
                    try:
                        response_data = json.loads(response)
                        result["response_type"] = response_data.get("type", "unknown")
                        logger.info(f"✓ Received response from {endpoint_path}: {result['response_type']}")
                    except json.JSONDecodeError:
                        logger.warning(f"⚠️ Non-JSON response from {endpoint_path}")
                        
                except asyncio.TimeoutError:
                    logger.warning(f"⚠️ Timeout waiting for response from {endpoint_path}")
                    result["error"] = "Response timeout"
                    
        except Exception as e:
            logger.error(f"❌ Failed to connect to {endpoint_path}: {e}")
            result["error"] = str(e)
            
        return result
        
    async def verify_all_endpoints(self) -> dict:
        """Verify all relevant WebSocket endpoints."""
        logger.info("Starting server setup verification")
        print("🔍 SERVER SETUP VERIFICATION")
        print("=" * 50)
        print(f"Server URL: {self.server_url}")
        print(f"Test Time: {self.verification_results['test_time']}")
        print("")
        
        # Define endpoints to test
        endpoints_to_test = [
            {
                "path": "/ws/mysql/test_user",
                "name": "MySQL Agent",
                "message": {"message": "SHOW TABLES;", "streaming": True}
            },
            {
                "path": "/ws/orchestration/test_user?mode=pattern_based",
                "name": "Orchestration (Pattern-based)",
                "message": {"message": "Test orchestration workflow", "streaming": True}
            },
            {
                "path": "/ws/orchestration/test_user?mode=orchestrator_based",
                "name": "Orchestration (Orchestrator-based)",
                "message": {"message": "Test orchestration workflow", "streaming": True}
            },
            {
                "path": "/ws/storage/test_user",
                "name": "Storage Analyzer",
                "message": {"message": "Test storage analysis", "streaming": True}
            },
            {
                "path": "/ws/alert/test_user",
                "name": "Alert Manager",
                "message": {"message": "Test alert management", "streaming": True}
            }
        ]
        
        # Test each endpoint
        for endpoint_config in endpoints_to_test:
            print(f"📋 Testing {endpoint_config['name']}...")
            
            result = await self.verify_endpoint(
                endpoint_config["path"], 
                endpoint_config["message"]
            )
            
            self.verification_results["endpoints"][endpoint_config["name"]] = result
            
            if result["accessible"] and result["response_received"]:
                status = "✅ PASS"
                print(f"  {status} - Response time: {result['response_time_seconds']:.2f}s")
            elif result["accessible"]:
                status = "⚠️ PARTIAL"
                print(f"  {status} - Connected but no response")
            else:
                status = "❌ FAIL"
                print(f"  {status} - {result['error']}")
                
            print("")
            
        # Check overall status
        accessible_count = sum(1 for r in self.verification_results["endpoints"].values() if r["accessible"])
        responsive_count = sum(1 for r in self.verification_results["endpoints"].values() if r["response_received"])
        total_count = len(self.verification_results["endpoints"])
        
        self.verification_results["summary"] = {
            "total_endpoints": total_count,
            "accessible_endpoints": accessible_count,
            "responsive_endpoints": responsive_count,
            "accessibility_rate": accessible_count / total_count,
            "responsiveness_rate": responsive_count / total_count
        }
        
        # Determine overall status
        # Require at least MySQL and one orchestration endpoint to be working
        mysql_working = self.verification_results["endpoints"].get("MySQL Agent", {}).get("response_received", False)
        orchestration_working = any(
            self.verification_results["endpoints"].get(name, {}).get("response_received", False)
            for name in ["Orchestration (Pattern-based)", "Orchestration (Orchestrator-based)"]
        )
        
        self.verification_results["overall_status"] = mysql_working and orchestration_working
        
        return self.verification_results
        
    def generate_verification_report(self) -> str:
        """Generate verification report."""
        report = []
        report.append("=" * 60)
        report.append("SERVER SETUP VERIFICATION REPORT")
        report.append("=" * 60)
        report.append(f"Server URL: {self.verification_results['server_url']}")
        report.append(f"Test Time: {self.verification_results['test_time']}")
        report.append(f"Overall Status: {'✅ READY' if self.verification_results['overall_status'] else '❌ NOT READY'}")
        report.append("")
        
        # Summary
        summary = self.verification_results.get("summary", {})
        if summary:
            report.append("SUMMARY:")
            report.append("-" * 20)
            report.append(f"Total Endpoints: {summary['total_endpoints']}")
            report.append(f"Accessible: {summary['accessible_endpoints']} ({summary['accessibility_rate']:.1%})")
            report.append(f"Responsive: {summary['responsive_endpoints']} ({summary['responsiveness_rate']:.1%})")
            report.append("")
            
        # Endpoint details
        report.append("ENDPOINT DETAILS:")
        report.append("-" * 30)
        
        for endpoint_name, result in self.verification_results["endpoints"].items():
            if result["accessible"] and result["response_received"]:
                status = "✅ WORKING"
                details = f"Response time: {result['response_time_seconds']:.2f}s"
            elif result["accessible"]:
                status = "⚠️ PARTIAL"
                details = "Connected but no response"
            else:
                status = "❌ FAILED"
                details = f"Error: {result['error']}"
                
            report.append(f"{endpoint_name}: {status}")
            report.append(f"  URL: {result['url']}")
            report.append(f"  Details: {details}")
            report.append("")
            
        # Recommendations
        report.append("RECOMMENDATIONS:")
        report.append("-" * 25)
        
        if self.verification_results["overall_status"]:
            report.append("✅ Server is ready for streaming tests")
            report.append("   You can proceed with running the orchestration streaming tests")
        else:
            report.append("❌ Server setup issues detected")
            
            mysql_working = self.verification_results["endpoints"].get("MySQL Agent", {}).get("response_received", False)
            if not mysql_working:
                report.append("   - MySQL agent endpoint is not working")
                report.append("   - Check if MCP servers are running on required ports")
                
            orchestration_working = any(
                self.verification_results["endpoints"].get(name, {}).get("response_received", False)
                for name in ["Orchestration (Pattern-based)", "Orchestration (Orchestrator-based)"]
            )
            if not orchestration_working:
                report.append("   - Orchestration endpoints are not working")
                report.append("   - Check orchestration runner initialization")
                
            report.append("   - Verify server is running: python main.py")
            report.append("   - Check server logs for errors")
            
        return "\n".join(report)


async def main():
    """Main verification function."""
    parser = argparse.ArgumentParser(description="Verify server setup for streaming tests")
    parser.add_argument("--server-url", default="ws://localhost:8000", 
                       help="WebSocket server URL (default: ws://localhost:8000)")
    args = parser.parse_args()
    
    verifier = ServerSetupVerifier(args.server_url)
    
    try:
        # Run verification
        results = await verifier.verify_all_endpoints()
        
        # Generate and display report
        report = verifier.generate_verification_report()
        print(report)
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"server_verification_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📄 Verification results saved to: {results_file}")
        
        # Exit with appropriate code
        exit_code = 0 if results["overall_status"] else 1
        print(f"\n🏁 Verification completed with exit code: {exit_code}")
        return exit_code
        
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        print(f"\n❌ Verification failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
