#!/usr/bin/env python3
"""
Comprehensive test runner for orchestration streaming validation.

This script runs all streaming tests to verify that the orchestrated shortage workflow
delivers real-time streaming for both LLM text chunks and MCP tool call events.

Tests included:
1. Orchestration streaming validation
2. Performance comparison with MySQL agent
3. Workflow stage progression verification
4. Real-time streaming latency analysis

Usage:
    python run_orchestration_streaming_tests.py [--server-url ws://localhost:8000]
"""

import asyncio
import argparse
import json
import logging
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

from test_orchestration_streaming_validation import OrchestrationStreamingValidator
from test_streaming_performance_comparison import StreamingPerformanceComparator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OrchestrationStreamingTestSuite:
    """Comprehensive test suite for orchestration streaming validation."""
    
    def __init__(self, server_url: str = "ws://localhost:8000"):
        self.server_url = server_url
        self.test_results = {
            "test_suite_start_time": datetime.now().isoformat(),
            "server_url": server_url,
            "tests": {},
            "overall_success": False,
            "summary": {}
        }
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all orchestration streaming tests."""
        logger.info("Starting comprehensive orchestration streaming test suite")
        print("🧪 ORCHESTRATION STREAMING TEST SUITE")
        print("=" * 60)
        print(f"Server URL: {self.server_url}")
        print(f"Start Time: {self.test_results['test_suite_start_time']}")
        print("")
        
        # Test 1: Orchestration Streaming Validation
        print("📋 Test 1: Orchestration Streaming Validation")
        print("-" * 50)
        try:
            validator = OrchestrationStreamingValidator(self.server_url)
            validation_results = await validator.test_orchestration_streaming()
            self.test_results["tests"]["orchestration_validation"] = validation_results
            
            if validation_results["success"]:
                print("✅ PASS - Orchestration streaming validation successful")
            else:
                print("❌ FAIL - Orchestration streaming validation failed")
                
        except Exception as e:
            logger.error(f"Test 1 failed with exception: {e}")
            self.test_results["tests"]["orchestration_validation"] = {
                "success": False,
                "error": str(e)
            }
            print(f"❌ FAIL - Test 1 exception: {e}")
            
        print("")
        
        # Test 2: Performance Comparison
        print("📋 Test 2: Streaming Performance Comparison")
        print("-" * 50)
        try:
            comparator = StreamingPerformanceComparator(self.server_url)
            comparison_results = await comparator.run_comparison_test()
            self.test_results["tests"]["performance_comparison"] = comparison_results
            
            if comparison_results["success"]:
                print("✅ PASS - Performance comparison successful")
            else:
                print("❌ FAIL - Performance comparison failed")
                
        except Exception as e:
            logger.error(f"Test 2 failed with exception: {e}")
            self.test_results["tests"]["performance_comparison"] = {
                "success": False,
                "error": str(e)
            }
            print(f"❌ FAIL - Test 2 exception: {e}")
            
        print("")
        
        # Test 3: Workflow Stage Verification
        print("📋 Test 3: Workflow Stage Verification")
        print("-" * 50)
        try:
            stage_results = await self._test_workflow_stages()
            self.test_results["tests"]["workflow_stages"] = stage_results
            
            if stage_results["success"]:
                print("✅ PASS - Workflow stage verification successful")
            else:
                print("❌ FAIL - Workflow stage verification failed")
                
        except Exception as e:
            logger.error(f"Test 3 failed with exception: {e}")
            self.test_results["tests"]["workflow_stages"] = {
                "success": False,
                "error": str(e)
            }
            print(f"❌ FAIL - Test 3 exception: {e}")
            
        print("")
        
        # Test 4: Real-time Latency Analysis
        print("📋 Test 4: Real-time Latency Analysis")
        print("-" * 50)
        try:
            latency_results = await self._test_realtime_latency()
            self.test_results["tests"]["latency_analysis"] = latency_results
            
            if latency_results["success"]:
                print("✅ PASS - Real-time latency analysis successful")
            else:
                print("❌ FAIL - Real-time latency analysis failed")
                
        except Exception as e:
            logger.error(f"Test 4 failed with exception: {e}")
            self.test_results["tests"]["latency_analysis"] = {
                "success": False,
                "error": str(e)
            }
            print(f"❌ FAIL - Test 4 exception: {e}")
            
        print("")
        
        # Generate summary
        self._generate_summary()
        
        # Determine overall success
        self.test_results["overall_success"] = all(
            test_result.get("success", False) 
            for test_result in self.test_results["tests"].values()
        )
        
        self.test_results["test_suite_end_time"] = datetime.now().isoformat()
        
        return self.test_results
        
    async def _test_workflow_stages(self) -> Dict[str, Any]:
        """Test that workflow stages are properly detected and streamed."""
        logger.info("Testing workflow stage progression")
        
        # Use the orchestration validator but focus on stage detection
        validator = OrchestrationStreamingValidator(self.server_url)
        results = await validator.test_orchestration_streaming("workflow_stage_test_user")
        
        # Analyze workflow stages
        workflow_stages = results.get("workflow_stages", [])
        expected_stages = ["stream_start", "mysql_analysis", "shortage_analysis", "alert_management", "stream_end"]
        
        detected_stages = [stage["stage"] for stage in workflow_stages]
        stage_coverage = len(set(detected_stages) & set(expected_stages)) / len(expected_stages)
        
        stage_results = {
            "success": stage_coverage >= 0.6,  # At least 60% of expected stages
            "workflow_stages": workflow_stages,
            "detected_stages": detected_stages,
            "expected_stages": expected_stages,
            "stage_coverage": stage_coverage,
            "analysis": {
                "mysql_detected": "mysql_analysis" in detected_stages,
                "shortage_detected": "shortage_analysis" in detected_stages,
                "alert_detected": "alert_management" in detected_stages,
                "proper_sequence": self._verify_stage_sequence(workflow_stages)
            }
        }
        
        return stage_results
        
    def _verify_stage_sequence(self, workflow_stages: List[Dict[str, Any]]) -> bool:
        """Verify that workflow stages appear in the correct sequence."""
        stage_order = [stage["stage"] for stage in workflow_stages]
        
        # Expected sequence patterns
        expected_patterns = [
            ["stream_start", "mysql_analysis", "shortage_analysis", "alert_management", "stream_end"],
            ["stream_start", "mysql_analysis", "shortage_analysis", "stream_end"],  # Alert might be optional
            ["stream_start", "mysql_analysis", "stream_end"]  # Minimal sequence
        ]
        
        # Check if any expected pattern is a subsequence of the actual sequence
        for pattern in expected_patterns:
            if self._is_subsequence(pattern, stage_order):
                return True
                
        return False
        
    def _is_subsequence(self, pattern: List[str], sequence: List[str]) -> bool:
        """Check if pattern is a subsequence of sequence."""
        pattern_idx = 0
        for item in sequence:
            if pattern_idx < len(pattern) and item == pattern[pattern_idx]:
                pattern_idx += 1
        return pattern_idx == len(pattern)
        
    async def _test_realtime_latency(self) -> Dict[str, Any]:
        """Test real-time streaming latency characteristics."""
        logger.info("Testing real-time streaming latency")
        
        # Use performance comparator to get detailed timing data
        comparator = StreamingPerformanceComparator(self.server_url)
        orchestration_results = await comparator.test_orchestration_streaming("latency_test_user")
        
        metrics = orchestration_results.get("streaming_metrics", {})
        events = orchestration_results.get("events", [])
        
        # Analyze inter-chunk latency
        chunk_events = [e for e in events if e["type"] == "stream_chunk"]
        inter_chunk_latencies = []
        
        for i in range(1, len(chunk_events)):
            latency = chunk_events[i]["timestamp"] - chunk_events[i-1]["timestamp"]
            inter_chunk_latencies.append(latency)
            
        # Analyze tool event latency
        tool_events = [e for e in events if e["type"] in ["mcp_tool_stream", "tool_stream"]]
        
        latency_results = {
            "success": True,
            "metrics": metrics,
            "latency_analysis": {
                "time_to_first_chunk": metrics.get("time_to_first_chunk_seconds", 0),
                "total_chunks": len(chunk_events),
                "total_tool_events": len(tool_events),
                "inter_chunk_latencies": inter_chunk_latencies,
                "average_inter_chunk_latency": sum(inter_chunk_latencies) / len(inter_chunk_latencies) if inter_chunk_latencies else 0,
                "max_inter_chunk_latency": max(inter_chunk_latencies) if inter_chunk_latencies else 0,
                "min_inter_chunk_latency": min(inter_chunk_latencies) if inter_chunk_latencies else 0
            }
        }
        
        # Evaluate latency criteria
        criteria = {
            "max_time_to_first_chunk": 10.0,  # 10 seconds
            "max_average_inter_chunk_latency": 2.0,  # 2 seconds
            "max_inter_chunk_latency": 5.0,  # 5 seconds
            "min_chunks": 5,
            "min_tool_events": 2
        }
        
        analysis = latency_results["latency_analysis"]
        
        # Check criteria
        checks = [
            analysis["time_to_first_chunk"] <= criteria["max_time_to_first_chunk"],
            analysis["average_inter_chunk_latency"] <= criteria["max_average_inter_chunk_latency"],
            analysis["max_inter_chunk_latency"] <= criteria["max_inter_chunk_latency"],
            analysis["total_chunks"] >= criteria["min_chunks"],
            analysis["total_tool_events"] >= criteria["min_tool_events"]
        ]
        
        latency_results["success"] = all(checks)
        latency_results["criteria_checks"] = {
            "time_to_first_chunk_ok": checks[0],
            "average_latency_ok": checks[1], 
            "max_latency_ok": checks[2],
            "sufficient_chunks": checks[3],
            "sufficient_tool_events": checks[4]
        }
        
        return latency_results
        
    def _generate_summary(self):
        """Generate test suite summary."""
        tests = self.test_results["tests"]
        
        summary = {
            "total_tests": len(tests),
            "passed_tests": sum(1 for test in tests.values() if test.get("success", False)),
            "failed_tests": sum(1 for test in tests.values() if not test.get("success", False)),
            "test_details": {}
        }
        
        for test_name, test_result in tests.items():
            summary["test_details"][test_name] = {
                "success": test_result.get("success", False),
                "has_error": "error" in test_result
            }
            
        summary["pass_rate"] = summary["passed_tests"] / summary["total_tests"] if summary["total_tests"] > 0 else 0
        
        self.test_results["summary"] = summary
        
    def generate_final_report(self) -> str:
        """Generate comprehensive final report."""
        report = []
        report.append("=" * 80)
        report.append("ORCHESTRATION STREAMING TEST SUITE - FINAL REPORT")
        report.append("=" * 80)
        report.append(f"Server URL: {self.test_results['server_url']}")
        report.append(f"Test Start: {self.test_results['test_suite_start_time']}")
        report.append(f"Test End: {self.test_results.get('test_suite_end_time', 'N/A')}")
        report.append(f"Overall Result: {'✅ PASS' if self.test_results['overall_success'] else '❌ FAIL'}")
        report.append("")
        
        # Summary
        summary = self.test_results.get("summary", {})
        if summary:
            report.append("TEST SUMMARY:")
            report.append("-" * 30)
            report.append(f"Total Tests: {summary['total_tests']}")
            report.append(f"Passed: {summary['passed_tests']}")
            report.append(f"Failed: {summary['failed_tests']}")
            report.append(f"Pass Rate: {summary['pass_rate']:.1%}")
            report.append("")
            
        # Individual test results
        report.append("INDIVIDUAL TEST RESULTS:")
        report.append("-" * 40)
        
        for test_name, test_result in self.test_results["tests"].items():
            status = "✅ PASS" if test_result.get("success", False) else "❌ FAIL"
            report.append(f"{test_name}: {status}")
            
            if "error" in test_result:
                report.append(f"  Error: {test_result['error']}")
                
        report.append("")
        
        # Key findings
        report.append("KEY FINDINGS:")
        report.append("-" * 20)
        
        # Orchestration validation findings
        orch_validation = self.test_results["tests"].get("orchestration_validation", {})
        if orch_validation.get("success"):
            metrics = orch_validation.get("streaming_metrics", {})
            report.append(f"✓ Orchestration streaming works with {metrics.get('total_chunks', 0)} text chunks")
            report.append(f"✓ Tool events streaming works with {metrics.get('total_tool_events', 0)} events")
            report.append(f"✓ Time to first chunk: {metrics.get('time_to_first_chunk_seconds', 0):.2f}s")
        else:
            report.append("❌ Orchestration streaming validation failed")
            
        # Performance comparison findings
        perf_comparison = self.test_results["tests"].get("performance_comparison", {})
        if perf_comparison.get("success"):
            report.append("✓ Orchestration streaming performance is competitive with MySQL agent")
        else:
            report.append("❌ Orchestration streaming performance is below expectations")
            
        # Workflow stages findings
        workflow_stages = self.test_results["tests"].get("workflow_stages", {})
        if workflow_stages.get("success"):
            coverage = workflow_stages.get("stage_coverage", 0)
            report.append(f"✓ Workflow stages detected with {coverage:.1%} coverage")
        else:
            report.append("❌ Workflow stage detection insufficient")
            
        # Latency findings
        latency_analysis = self.test_results["tests"].get("latency_analysis", {})
        if latency_analysis.get("success"):
            analysis = latency_analysis.get("latency_analysis", {})
            avg_latency = analysis.get("average_inter_chunk_latency", 0)
            report.append(f"✓ Real-time latency acceptable (avg: {avg_latency:.2f}s between chunks)")
        else:
            report.append("❌ Real-time latency requirements not met")
            
        return "\n".join(report)


async def main():
    """Main test execution function."""
    parser = argparse.ArgumentParser(description="Run orchestration streaming tests")
    parser.add_argument("--server-url", default="ws://localhost:8000", 
                       help="WebSocket server URL (default: ws://localhost:8000)")
    args = parser.parse_args()
    
    test_suite = OrchestrationStreamingTestSuite(args.server_url)
    
    try:
        # Run all tests
        results = await test_suite.run_all_tests()
        
        # Generate and display final report
        final_report = test_suite.generate_final_report()
        print(final_report)
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"orchestration_streaming_test_suite_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed results saved to: {results_file}")
        
        # Exit with appropriate code
        exit_code = 0 if results["overall_success"] else 1
        print(f"\n🏁 Test suite completed with exit code: {exit_code}")
        return exit_code
        
    except Exception as e:
        logger.error(f"❌ Test suite execution failed: {e}")
        print(f"\n❌ Test suite execution failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
