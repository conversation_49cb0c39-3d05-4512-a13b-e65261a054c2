#!/usr/bin/env python3
"""
Stream Validation Test Runner

Simple runner script for executing agent streaming validation tests.
"""

import asyncio
import json
import logging
import os
import sys
from test_agent_streaming_validation import run_comprehensive_streaming_tests

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('streaming_test_execution.log')
    ]
)
logger = logging.getLogger(__name__)


def print_banner():
    """Print test banner."""
    banner = """
    ╔══════════════════════════════════════════════════════════════════╗
    ║               MCP Agent Streaming Validation Tests               ║
    ║                                                                  ║
    ║  Testing tool call streaming and LLM streaming for:              ║
    ║  • MySQL Agent                                                   ║
    ║  • Storage Analyzer Agent                                        ║
    ║  • Alert Manager Agent                                           ║
    ╚══════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def print_summary(report):
    """Print test summary."""
    success_rate = report.get('success_rate', 0)
    successful = report.get('successful_tests', 0)
    total = report.get('total_tests', 0)
    
    status_color = '\033[92m' if success_rate >= 0.8 else '\033[91m' if success_rate < 0.5 else '\033[93m'
    reset_color = '\033[0m'
    
    summary = f"""
    {status_color}╔══════════════ TEST RESULTS ══════════════╗{reset_color}
    {status_color}║  Success Rate: {success_rate:.1%}                     ║{reset_color}
    {status_color}║  Passed:       {successful:2d}/{total:2d} tests                  ║{reset_color}
    {status_color}╚═══════════════════════════════════════════╝{reset_color}
    
    📊 Agent Performance:
    """
    
    agent_performance = report.get('agent_performance', {})
    for agent, metrics in agent_performance.items():
        avg_duration = metrics.get('average_duration', 0)
        agent_success_rate = metrics.get('success_rate', 0)
        summary += f"   • {agent.title():15} {agent_success_rate:.1%} success, {avg_duration:.2f}s avg\n"
        
    overall = report.get('overall_performance', {})
    if overall:
        summary += f"\n   📈 Overall Avg Duration: {overall.get('average_duration', 0):.2f}s"
        summary += f"\n   ⚡ Average Latency:      {overall.get('average_latency', 0):.3f}s"
        
    print(summary)


async def check_server_running():
    """Check if WebSocket server is running."""
    try:
        import websockets
        websocket = await websockets.connect("ws://localhost:8000/ws/mysql/health_check")
        await websocket.close()
        return True
    except Exception:
        return False


async def main():
    """Main test execution."""
    print_banner()
    
    # Pre-flight checks
    logger.info("🔍 Running pre-flight checks...")
    
    # Check if server is running
    if not await check_server_running():
        logger.warning("⚠️  WebSocket server may not be running on localhost:8000")
        logger.info("💡 Start the server with: python main.py")
        try:
            response = input("\n📝 Continue with tests anyway? (y/N): ")
        except EOFError:
            # Non-interactive environment, continue with tests
            logger.info("Running in non-interactive mode, continuing with tests...")
            response = 'y'
        if response.lower() != 'y':
            logger.info("Tests cancelled by user")
            return
    else:
        logger.info("✅ WebSocket server is responding")
    
    # Check test scenarios file
    if not os.path.exists("test_scenarios.json"):
        logger.error("❌ test_scenarios.json not found")
        return
    else:
        logger.info("✅ Test scenarios loaded")
        
    # Run tests
    logger.info("🚀 Starting comprehensive streaming validation tests...")
    
    try:
        report = await run_comprehensive_streaming_tests()
        
        # Print summary
        print_summary(report)
        
        # Show failures if any
        failed_tests = [r for r in report.get('test_details', []) if not r.get('success', False)]
        if failed_tests:
            logger.info(f"\n❌ Failed Tests ({len(failed_tests)}):")
            for i, test in enumerate(failed_tests, 1):
                logger.info(f"   {i}. {test.get('endpoint', 'unknown')}: {test.get('errors', ['Unknown error'])[0]}")
                
        logger.info(f"\n📄 Full report saved to streaming_validation_report_*.json")
        logger.info("🎉 Test execution complete!")
        
    except KeyboardInterrupt:
        logger.info("\n⏹️  Tests interrupted by user")
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        raise


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")