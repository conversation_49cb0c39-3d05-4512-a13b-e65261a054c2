"""
Financial Orchestrator for MCP Agent System
==========================================

A specialized orchestrator that coordinates three financial analysis agents:
- MySQL Agent: Historical data analysis and database queries
- Storage Analyzer Agent: Shortage analysis and inventory assessment  
- Alert Manager Agent: Notification and alert processing

This orchestrator extends the base MCP Orchestrator pattern and implements
financial-specific query processing, workflow routing, and context management.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable, Coroutine, Literal
from dataclasses import dataclass, field

from mcp_agent.agents.agent import Agent
from mcp_agent.workflows.orchestrator.orchestrator import Orchestrator
from mcp_agent.workflows.orchestrator.orchestrator_models import (
    Plan, PlanResult, Step, StepResult, TaskWithResult, AgentTask
)
from mcp_agent.workflows.llm.augmented_llm import (
    AugmentedLLM, MessageParamT, MessageT, RequestParams
)
from mcp_agent.core.context import Context
from mcp_agent.logging.logger import get_logger

logger = get_logger(__name__)


@dataclass
class FinancialQueryComponents:
    """Parsed components of a financial analysis query."""
    query_type: str  # "shortage", "supplier_risk", "customer_priority", "comprehensive"
    entities: Dict[str, Any] = field(default_factory=dict)  # Extracted entities (orders, materials, etc.)
    parameters: Dict[str, Any] = field(default_factory=dict)  # Query parameters (dates, thresholds)
    required_agents: List[str] = field(default_factory=list)  # Agents needed for this query
    workflow_pattern: Optional[str] = None  # Predefined workflow to use


@dataclass
class FinancialAnalysisContext:
    """Enhanced context for financial analysis workflows."""
    original_query: str
    query_components: FinancialQueryComponents
    mysql_results: Dict[str, Any] = field(default_factory=dict)
    shortage_results: Dict[str, Any] = field(default_factory=dict)
    alert_results: Dict[str, Any] = field(default_factory=dict)
    context_metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_shared_data(self) -> str:
        """Format all context data for agent consumption."""
        context_parts = []
        
        if self.mysql_results:
            context_parts.append(f"MySQL Analysis Results:\n{self._format_mysql_data()}")
            
        if self.shortage_results:
            context_parts.append(f"Shortage Analysis Results:\n{self._format_shortage_data()}")
            
        if self.alert_results:
            context_parts.append(f"Alert Processing Results:\n{self._format_alert_data()}")
            
        return "\n\n".join(context_parts) if context_parts else "No previous analysis data available."
    
    def _format_mysql_data(self) -> str:
        """Format MySQL results for context sharing."""
        if "response" in self.mysql_results:
            return f"Database Query Results: {self.mysql_results['response']}"
        return "MySQL analysis completed with no specific results"
        
    def _format_shortage_data(self) -> str:
        """Format shortage analysis results for context sharing."""
        parts = []
        if "shortage_index" in self.shortage_results:
            parts.append(f"Shortage Index: {self.shortage_results['shortage_index']:.3f}")
        if "risk_level" in self.shortage_results:
            parts.append(f"Risk Level: {self.shortage_results['risk_level']}")
        if "response" in self.shortage_results:
            parts.append(f"Analysis: {self.shortage_results['response']}")
        return "; ".join(parts) if parts else "Shortage analysis completed"
    
    def _format_alert_data(self) -> str:
        """Format alert results for context sharing."""
        parts = []
        if "alerts_sent" in self.alert_results:
            parts.append(f"Alerts Sent: {len(self.alert_results['alerts_sent'])}")
        if "alert_summary" in self.alert_results:
            parts.append(f"Summary: {self.alert_results['alert_summary']}")
        return "; ".join(parts) if parts else "Alert processing completed"


class FinancialOrchestrator(Orchestrator[MessageParamT, MessageT]):
    """
    Specialized orchestrator for financial analysis workflows.
    
    Coordinates MySQL Agent → Storage Analyzer Agent → Alert Manager Agent
    with intelligent query processing and context management.
    """
    
    def __init__(
        self,
        llm_factory: Callable[[Agent], AugmentedLLM[MessageParamT, MessageT]],
        mysql_agent: Any,
        shortage_agent: Any, 
        alert_agent: Any,
        name: str = "FinancialOrchestrator",
        context: Optional[Context] = None,
        **kwargs,
    ):
        """
        Initialize the Financial Orchestrator.
        
        Args:
            llm_factory: Factory function to create LLMs for agents
            mysql_agent: MySQL database analysis agent
            shortage_agent: Storage/shortage analysis agent
            alert_agent: Alert management agent
            name: Name of the orchestrator
            context: Application context
        """
        # Create agent wrappers for orchestration
        available_agents = [
            self._create_agent_wrapper("mysql_analyzer", mysql_agent),
            self._create_agent_wrapper("shortage_analyzer", shortage_agent),
            self._create_agent_wrapper("alert_manager", alert_agent),
        ]
        
        super().__init__(
            llm_factory=llm_factory,
            name=name,
            available_agents=available_agents,
            plan_type="full",  # Use full planning for predictable financial workflows
            context=context,
            **kwargs,
        )
        
        # Store agent references for direct access
        self.mysql_agent = mysql_agent
        self.shortage_agent = shortage_agent
        self.alert_agent = alert_agent
        
        # Financial analysis context
        self.current_analysis_context: Optional[FinancialAnalysisContext] = None
        
        logger.info(f"FinancialOrchestrator '{name}' initialized with 3 agents")
    
    def _create_agent_wrapper(self, agent_name: str, agent_instance: Any) -> Agent:
        """Create an Agent wrapper for the orchestrator framework."""
        return Agent(
            name=agent_name,
            instruction=f"Execute {agent_name} tasks for financial analysis",
        )
    
    async def analyze_financial_query(
        self, 
        query: str, 
        request_params: Optional[RequestParams] = None
    ) -> Dict[str, Any]:
        """
        Main entry point for financial analysis queries.
        
        Args:
            query: Natural language financial analysis query
            request_params: Optional request parameters
            
        Returns:
            Dict containing comprehensive analysis results
        """
        logger.info(f"Starting financial analysis for query: {query}")
        
        try:
            # Parse the query to determine workflow
            query_components = self._parse_financial_query(query)
            
            # Create analysis context
            self.current_analysis_context = FinancialAnalysisContext(
                original_query=query,
                query_components=query_components
            )
            
            # Execute the appropriate workflow
            if query_components.workflow_pattern == "shortage_analysis":
                return await self._execute_shortage_workflow(query, request_params)
            elif query_components.workflow_pattern == "supplier_risk":
                return await self._execute_supplier_risk_workflow(query, request_params)
            elif query_components.workflow_pattern == "customer_priority":
                return await self._execute_customer_priority_workflow(query, request_params)
            else:
                # Default comprehensive workflow
                return await self._execute_comprehensive_workflow(query, request_params)
                
        except Exception as e:
            logger.error(f"Financial analysis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "analysis_type": "error"
            }
    
    def _parse_financial_query(self, query: str) -> FinancialQueryComponents:
        """Parse a financial query to determine workflow and extract components."""
        query_lower = query.lower()
        
        # Initialize components
        components = FinancialQueryComponents(query_type="unknown")
        
        # Determine query type and workflow pattern
        if any(term in query_lower for term in ["shortage", "inventory", "stock", "availability"]):
            components.query_type = "shortage"
            components.workflow_pattern = "shortage_analysis"
            components.required_agents = ["mysql_analyzer", "shortage_analyzer", "alert_manager"]
            
        elif any(term in query_lower for term in ["supplier", "vendor", "delivery", "reliability"]):
            components.query_type = "supplier_risk"
            components.workflow_pattern = "supplier_risk"
            components.required_agents = ["mysql_analyzer", "shortage_analyzer"]
            
        elif any(term in query_lower for term in ["customer", "priority", "order", "delivery date"]):
            components.query_type = "customer_priority"
            components.workflow_pattern = "customer_priority"
            components.required_agents = ["mysql_analyzer", "alert_manager"]
            
        else:
            # Comprehensive analysis for complex or unclear queries
            components.query_type = "comprehensive"
            components.workflow_pattern = "comprehensive"
            components.required_agents = ["mysql_analyzer", "shortage_analyzer", "alert_manager"]
        
        # Extract entities (order numbers, material codes, etc.)
        components.entities = self._extract_entities(query)
        
        # Extract parameters (dates, quantities, thresholds)
        components.parameters = self._extract_parameters(query)
        
        logger.info(f"Parsed query type: {components.query_type}, workflow: {components.workflow_pattern}")
        return components
    
    def _extract_entities(self, query: str) -> Dict[str, List[str]]:
        """Extract named entities from the query."""
        import re
        entities = {}
        
        # Extract order numbers (CUSTORD-YYYYMMXXX pattern)
        order_pattern = r'CUSTORD-\d{9}'
        orders = re.findall(order_pattern, query)
        if orders:
            entities['orders'] = orders
            
        # Extract work order numbers (WO-YYYYMMXXX pattern)
        wo_pattern = r'WO-\d{9}'
        work_orders = re.findall(wo_pattern, query)
        if work_orders:
            entities['work_orders'] = work_orders
            
        # Extract material codes (various patterns)
        material_patterns = [
            r'[A-Z]{2,4}\d{4,6}',  # MM2004, HCS500, etc.
            r'[A-Z]+_[A-Z0-9_]+',  # DDR5_32GB, etc.
        ]
        materials = []
        for pattern in material_patterns:
            materials.extend(re.findall(pattern, query))
        if materials:
            entities['materials'] = list(set(materials))  # Remove duplicates
            
        return entities
    
    def _extract_parameters(self, query: str) -> Dict[str, Any]:
        """Extract numerical and date parameters from the query."""
        import re
        parameters = {}
        
        # Extract quantities
        quantity_patterns = [
            r'(\d+)\s*(?:units?|pieces?|items?)',
            r'require[sd]?\s*(?:is\s*)?(\d+)',
            r'available\s*(?:is\s*)?(\d+)',
        ]
        
        for pattern in quantity_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            if matches:
                parameters['quantities'] = [int(m) for m in matches]
                break
                
        # Extract dates
        date_patterns = [
            r'\d{4}-\d{2}-\d{2}',  # YYYY-MM-DD
            r'\d{1,2}/\d{1,2}/\d{4}',  # M/D/YYYY
            r'(?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2},?\s+\d{4}',
        ]
        
        for pattern in date_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            if matches:
                parameters['dates'] = matches
                break
                
        return parameters
    
    async def _execute_shortage_workflow(
        self, 
        query: str, 
        request_params: Optional[RequestParams] = None
    ) -> Dict[str, Any]:
        """Execute the shortage analysis workflow: MySQL → Shortage → Alert."""
        logger.info("Executing shortage analysis workflow")
        
        try:
            # Step 1: MySQL Analysis
            mysql_result = await self._execute_mysql_analysis(query)
            if mysql_result and mysql_result.get("success", True):
                self.current_analysis_context.mysql_results = mysql_result
            
            # Step 2: Shortage Analysis (with MySQL context)
            shortage_result = await self._execute_shortage_analysis(
                query, 
                context=self.current_analysis_context.get_shared_data()
            )
            if shortage_result and shortage_result.get("success", True):
                self.current_analysis_context.shortage_results = shortage_result
            
            # Step 3: Alert Management (with full context)
            alert_result = await self._execute_alert_management(
                query,
                shortage_result=shortage_result,
                context=self.current_analysis_context.get_shared_data()
            )
            if alert_result and alert_result.get("success", True):
                self.current_analysis_context.alert_results = alert_result
            
            # Compile comprehensive results
            return {
                "success": True,
                "analysis_type": "shortage_workflow",
                "query": query,
                "mysql_analysis": mysql_result,
                "shortage_analysis": shortage_result, 
                "alert_management": alert_result,
                "context": self.current_analysis_context.get_shared_data()
            }
            
        except Exception as e:
            logger.error(f"Shortage workflow failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis_type": "shortage_workflow"
            }
    
    async def _execute_supplier_risk_workflow(
        self, 
        query: str,
        request_params: Optional[RequestParams] = None
    ) -> Dict[str, Any]:
        """Execute supplier risk assessment workflow: MySQL → Shortage."""
        logger.info("Executing supplier risk workflow")
        
        try:
            # Step 1: MySQL Analysis for supplier data
            mysql_result = await self._execute_mysql_analysis(query)
            if mysql_result and mysql_result.get("success", True):
                self.current_analysis_context.mysql_results = mysql_result
            
            # Step 2: Risk-weighted Shortage Analysis
            shortage_result = await self._execute_shortage_analysis(
                query,
                context=self.current_analysis_context.get_shared_data()
            )
            if shortage_result and shortage_result.get("success", True):
                self.current_analysis_context.shortage_results = shortage_result
            
            return {
                "success": True,
                "analysis_type": "supplier_risk_workflow", 
                "query": query,
                "mysql_analysis": mysql_result,
                "shortage_analysis": shortage_result,
                "context": self.current_analysis_context.get_shared_data()
            }
            
        except Exception as e:
            logger.error(f"Supplier risk workflow failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis_type": "supplier_risk_workflow"
            }
    
    async def _execute_customer_priority_workflow(
        self,
        query: str,
        request_params: Optional[RequestParams] = None
    ) -> Dict[str, Any]:
        """Execute customer priority workflow: MySQL → Alert."""
        logger.info("Executing customer priority workflow")
        
        try:
            # Step 1: MySQL Analysis for customer data
            mysql_result = await self._execute_mysql_analysis(query)
            if mysql_result and mysql_result.get("success", True):
                self.current_analysis_context.mysql_results = mysql_result
            
            # Step 2: Alert Management for customer notifications
            alert_result = await self._execute_alert_management(
                query,
                shortage_result=None,  # No shortage analysis in this workflow
                context=self.current_analysis_context.get_shared_data()
            )
            if alert_result and alert_result.get("success", True):
                self.current_analysis_context.alert_results = alert_result
            
            return {
                "success": True,
                "analysis_type": "customer_priority_workflow",
                "query": query,
                "mysql_analysis": mysql_result,
                "alert_management": alert_result,
                "context": self.current_analysis_context.get_shared_data()
            }
            
        except Exception as e:
            logger.error(f"Customer priority workflow failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis_type": "customer_priority_workflow"
            }
    
    async def _execute_comprehensive_workflow(
        self,
        query: str,
        request_params: Optional[RequestParams] = None
    ) -> Dict[str, Any]:
        """Execute comprehensive workflow: MySQL → Shortage → Alert."""
        logger.info("Executing comprehensive analysis workflow")
        # This is the same as shortage workflow but with different labeling
        result = await self._execute_shortage_workflow(query, request_params)
        result["analysis_type"] = "comprehensive_workflow"
        return result
    
    async def _execute_mysql_analysis(self, query: str) -> Optional[Dict[str, Any]]:
        """Execute MySQL analysis with the MySQL agent."""
        logger.info("Executing MySQL analysis step")
        
        try:
            # Import required components
            from agents.mysql_agent import (
                MCPOrchestratorInputSchema, 
                safe_orchestrator_run, 
                tool_schema_to_class_map,
                FinalResponseSchema
            )
            
            # Create input for MySQL agent
            mysql_input = MCPOrchestratorInputSchema(query=query)
            
            # Execute with timeout
            mysql_output = safe_orchestrator_run(self.mysql_agent, mysql_input)
            action_instance = mysql_output.action
            reasoning = mysql_output.reasoning
            
            # Continue execution until final response
            max_iterations = 10
            iteration = 0
            
            while not isinstance(action_instance, FinalResponseSchema) and iteration < max_iterations:
                schema_type = type(action_instance)
                ToolClass = tool_schema_to_class_map.get(schema_type)
                
                if not ToolClass:
                    logger.error(f"Unknown schema type: {schema_type.__name__}")
                    break
                
                tool_name = getattr(ToolClass, 'mcp_tool_name', 'unknown_tool')
                logger.info(f"Executing MySQL tool: {tool_name} (iteration {iteration+1})")
                
                tool_instance = ToolClass()
                tool_output = tool_instance.run(action_instance)
                
                # Add result to agent memory and continue using safe method
                result_message = MCPOrchestratorInputSchema(
                    query=f"Tool {tool_name} executed with result: {tool_output.result}"
                )
                # Use safe memory addition method
                def add_memory_message(agent, role, content):
                    """Safely add message to agent memory, handling BaseAgentWrapper."""
                    try:
                        # Check if it's a BaseAgentWrapper with internal agent
                        if hasattr(agent, 'agent') and hasattr(agent.agent, 'memory'):
                            agent.agent.memory.add_message(role, content)
                        # Check if it's a direct agent with memory
                        elif hasattr(agent, 'memory'):
                            agent.memory.add_message(role, content)
                        # Check if it's a BaseAgentWrapper with _base_agent
                        elif hasattr(agent, '_base_agent') and agent._base_agent and hasattr(agent._base_agent, 'memory'):
                            agent._base_agent.memory.add_message(role, content)
                        else:
                            logger.warning(f"Could not add memory message to agent {type(agent).__name__}: no memory attribute found")
                    except Exception as e:
                        logger.warning(f"Failed to add memory message to agent: {e}")

                add_memory_message(self.mysql_agent, "system", result_message)
                
                mysql_output = safe_orchestrator_run(self.mysql_agent)
                action_instance = mysql_output.action
                reasoning = mysql_output.reasoning
                iteration += 1
            
            # Extract final response
            if isinstance(action_instance, FinalResponseSchema):
                response = action_instance.response_text
                logger.info("✓ MySQL analysis completed successfully")
                return {
                    "success": True,
                    "response": response,
                    "context": response,
                    "reasoning": reasoning
                }
            else:
                logger.warning("MySQL analysis did not complete successfully")
                return {
                    "success": False,
                    "response": f"Analysis incomplete after {max_iterations} iterations",
                    "context": reasoning
                }
                
        except Exception as e:
            logger.error(f"MySQL analysis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": f"MySQL analysis error: {str(e)}"
            }
    
    async def _execute_shortage_analysis(
        self, 
        query: str,
        context: str = ""
    ) -> Optional[Dict[str, Any]]:
        """Execute shortage analysis with the shortage agent."""
        logger.info("Executing shortage analysis step")
        
        try:
            # Prepare shortage analysis input with context
            enhanced_message = "Analyze component shortage risk using MCP SSE transport"
            if context:
                enhanced_message += f"\n\nHistorical Context: {context}"
            
            shortage_input = {
                "company_name": "FinancialAnalysis",
                "financial_data": query,  # Use query as financial data
                "message": enhanced_message
            }
            
            # Execute shortage analysis
            result = await self.shortage_agent.enhanced_shortage_analysis(shortage_input)
            
            logger.info("✓ Shortage analysis completed successfully")
            return {
                "success": True,
                "shortage_index": result.shortage_index,
                "risk_level": result.risk_level,
                "response": result.response,
                "company_name": result.company_name
            }
            
        except Exception as e:
            logger.error(f"Shortage analysis failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _execute_alert_management(
        self,
        query: str,
        shortage_result: Optional[Dict[str, Any]] = None,
        context: str = ""
    ) -> Optional[Dict[str, Any]]:
        """Execute alert management with the alert agent."""
        logger.info("Executing alert management step")
        
        try:
            from schemas.agent_schemas import AlertManagementInputSchema
            
            # Prepare alert data
            shortage_data = ""
            analysis_data = context
            
            if shortage_result and shortage_result.get("success", False):
                shortage_data = (
                    f"shortage_index is {shortage_result['shortage_index']:.3f}, "
                    f"risk_level is {shortage_result['risk_level']}"
                )
                analysis_data = shortage_result.get("response", context)
            
            # Create alert input
            alert_input = AlertManagementInputSchema(
                company_name="FinancialAnalysis", 
                analysis_data=analysis_data,
                shortage_data=shortage_data,
                alert_message=f"Financial analysis alert based on: {query}",
                message="Process financial analysis results and send appropriate notifications"
            )
            
            # Execute alert processing
            alert_result = await self.alert_agent.process_financial_analysis(alert_input)
            
            logger.info("✓ Alert management completed successfully")
            return {
                "success": True,
                "alerts_sent": alert_result.alerts_sent,
                "notification_results": alert_result.notification_results,
                "alert_summary": alert_result.alert_summary
            }
            
        except Exception as e:
            logger.error(f"Alert management failed: {e}")
            # Don't fail the entire workflow for alert issues
            return {
                "success": True,  # Mark as successful to continue workflow
                "error": str(e),
                "alerts_sent": [],
                "notification_results": [],
                "alert_summary": f"Alert processing had issues: {str(e)}"
            }