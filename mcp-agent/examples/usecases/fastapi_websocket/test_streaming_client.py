#!/usr/bin/env python3
"""
Simple WebSocket client to test streaming functionality
"""

import asyncio
import json
import logging
import sys
import websockets
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'streaming_test_client_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)


class StreamingTestClient:
    """Simple WebSocket client for testing streaming functionality."""
    
    def __init__(self, base_url: str = "ws://localhost:8000"):
        self.base_url = base_url
        self.websocket = None
        self.user_id = f"test_user_{datetime.now().strftime('%H%M%S')}"
        
    async def connect(self):
        """Connect to the WebSocket server."""
        try:
            url = f"{self.base_url}/ws/{self.user_id}"
            logger.info(f"Connecting to: {url}")
            
            self.websocket = await websockets.connect(url)
            logger.info("✅ Connected successfully!")
            
            # Listen for welcome message
            welcome_msg = await self.websocket.recv()
            welcome_data = json.loads(welcome_msg)
            logger.info(f"Welcome message: {welcome_data.get('message', 'No message')}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Connection failed: {e}")
            return False
    
    async def send_message(self, message: str, streaming: bool = True):
        """Send a message and handle streaming response."""
        if not self.websocket:
            logger.error("Not connected to WebSocket")
            return
            
        try:
            # Send message
            message_data = {
                "message": message,
                "streaming": streaming
            }
            await self.websocket.send(json.dumps(message_data))
            logger.info(f"📤 Sent: {message}")
            
            # Handle streaming response
            stream_chunks = []
            tool_events = []
            
            while True:
                try:
                    response = await asyncio.wait_for(self.websocket.recv(), timeout=30.0)
                    data = json.loads(response)
                    
                    msg_type = data.get("type", "unknown")
                    
                    if msg_type == "stream_start":
                        logger.info(f"🚀 Stream started: {data.get('message', '')}")
                        
                    elif msg_type == "stream_chunk":
                        chunk = data.get("message", "")
                        stream_chunks.append(chunk)
                        print(chunk, end="", flush=True)  # Real-time output
                        
                    elif msg_type == "tool_stream":
                        tool_data = data.get("data", {})
                        tool_events.append(tool_data)
                        logger.info(f"🔧 Tool event: {tool_data.get('type', 'unknown')} - {tool_data.get('tool_name', 'unknown')}")
                        
                    elif msg_type == "stream_end":
                        logger.info(f"\n✅ Stream completed: {data.get('message', '')}")
                        break
                        
                    elif msg_type == "result":
                        logger.info(f"📥 Result: {data.get('message', '')}")
                        break
                        
                    elif msg_type == "error":
                        logger.error(f"❌ Error: {data.get('message', '')}")
                        break
                        
                    else:
                        logger.info(f"📨 Received: {msg_type} - {data}")
                        
                except asyncio.TimeoutError:
                    logger.warning("⏰ Response timeout")
                    break
                    
            # Summary
            logger.info(f"📊 Summary: {len(stream_chunks)} chunks, {len(tool_events)} tool events")
            return {
                "chunks": stream_chunks,
                "tool_events": tool_events,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"❌ Error sending message: {e}")
            return {"success": False, "error": str(e)}
    
    async def disconnect(self):
        """Disconnect from the WebSocket server."""
        if self.websocket:
            await self.websocket.close()
            logger.info("👋 Disconnected")


async def test_streaming():
    """Test streaming functionality."""
    client = StreamingTestClient()
    
    # Connect
    if not await client.connect():
        return False
    
    try:
        # Test 1: Simple query
        logger.info("\n" + "="*50)
        logger.info("🧪 Test 1: Simple Query")
        logger.info("="*50)
        
        result1 = await client.send_message("What is the current time?", streaming=True)
        
        # Test 2: More complex query that might trigger tools
        logger.info("\n" + "="*50)
        logger.info("🧪 Test 2: Complex Query")
        logger.info("="*50)
        
        result2 = await client.send_message("Can you help me analyze some data and create a report?", streaming=True)
        
        # Test 3: Non-streaming mode
        logger.info("\n" + "="*50)
        logger.info("🧪 Test 3: Non-Streaming Mode")
        logger.info("="*50)
        
        result3 = await client.send_message("Hello, how are you?", streaming=False)
        
        # Results summary
        logger.info("\n" + "="*50)
        logger.info("📋 Test Results Summary")
        logger.info("="*50)
        
        tests = [
            ("Simple Query", result1),
            ("Complex Query", result2),
            ("Non-Streaming", result3)
        ]
        
        for test_name, result in tests:
            if result and result.get("success"):
                chunks = len(result.get("chunks", []))
                tools = len(result.get("tool_events", []))
                logger.info(f"✅ {test_name}: {chunks} chunks, {tools} tool events")
            else:
                logger.info(f"❌ {test_name}: Failed")
        
        return True
        
    finally:
        await client.disconnect()


if __name__ == "__main__":
    print("🚀 Starting WebSocket Streaming Test")
    print("="*60)
    
    try:
        success = asyncio.run(test_streaming())
        if success:
            print("\n🎉 All tests completed!")
        else:
            print("\n💥 Tests failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        sys.exit(1)
