{"level":"INFO","timestamp":"2025-09-17T13:38:46.451094","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T13:38:46.453115","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T13:38:46.453147","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:38:46.453171","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:38:46.453190","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:38:46.453216","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.453234","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"d986eea1-aa81-417b-8b7e-52e0e744e90c"}}}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.527173","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.528295","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.529114","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.529162","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.529244","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.529259","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.529575","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.529796","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.529882","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.529898","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.529912","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.529927","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.529947","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.529956","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.529971","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.529979","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.529990","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.530095","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.530114","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.530134","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.530144","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.530158","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.530169","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T13:38:46.530285","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T13:39:08.247147","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T13:39:08.247312","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T13:39:08.247336","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:39:08.247360","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:39:08.247375","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:39:08.247388","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T13:39:08.247404","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_mysql_demo_mysql_client","agent_name":"mcp_application_loop","session_id":"6c139ef0-00a0-4877-b9eb-176a8f9626e7"}}}
{"level":"INFO","timestamp":"2025-09-17T13:39:08.248843","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Financial session initialized for user demo_mysql_client, type: mysql"}
{"level":"INFO","timestamp":"2025-09-17T13:39:20.674408","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_mysql_demo_mysql_client","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T13:39:33.458782","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T13:39:33.458979","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T13:39:33.459003","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:39:33.459022","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:39:33.459037","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:39:33.459051","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T13:39:33.459069","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_mysql_demo_mysql_client","agent_name":"mcp_application_loop","session_id":"3d87b8be-e872-4fc1-b8a6-ce66109b4085"}}}
{"level":"INFO","timestamp":"2025-09-17T13:39:33.460702","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Financial session initialized for user demo_mysql_client, type: mysql"}
{"level":"INFO","timestamp":"2025-09-17T13:39:41.765786","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_mysql_demo_mysql_client","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T13:54:00.843369","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T13:54:00.843823","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T13:54:00.843873","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:54:00.843900","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:54:00.843922","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:54:00.843935","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T13:54:00.843962","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_mysql_demo_mysql_client","agent_name":"mcp_application_loop","session_id":"abd6e31a-91b2-413f-b32d-3cf4b9082ae5"}}}
{"level":"INFO","timestamp":"2025-09-17T13:54:00.846956","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Financial session initialized for user demo_mysql_client, type: mysql"}
{"level":"INFO","timestamp":"2025-09-17T13:54:08.511530","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_mysql_demo_mysql_client","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T13:56:34.094639","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T13:56:34.094975","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T13:56:34.095005","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:56:34.095029","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:56:34.095043","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:56:34.095059","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T13:56:34.095082","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_mysql_demo_mysql_client","agent_name":"mcp_application_loop","session_id":"9838a923-0152-4580-acb4-ab02f3370a05"}}}
{"level":"INFO","timestamp":"2025-09-17T13:56:34.097354","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Financial session initialized for user demo_mysql_client, type: mysql"}
{"level":"INFO","timestamp":"2025-09-17T13:56:53.381938","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_mysql_demo_mysql_client","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T13:57:31.386553","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T13:57:31.386732","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T13:57:31.386764","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:57:31.386783","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:57:31.386797","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T13:57:31.386810","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T13:57:31.386825","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_mysql_demo_mysql_client","agent_name":"mcp_application_loop","session_id":"8950e474-87c5-44c6-96b0-bc92d7bf9439"}}}
{"level":"INFO","timestamp":"2025-09-17T13:57:31.388324","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"Financial session initialized for user demo_mysql_client, type: mysql"}
{"level":"INFO","timestamp":"2025-09-17T13:58:18.073470","namespace":"mcp_agent.financial_websocket_mysql_demo_mysql_client","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_mysql_demo_mysql_client","agent_name":"mcp_application_loop"}}}
