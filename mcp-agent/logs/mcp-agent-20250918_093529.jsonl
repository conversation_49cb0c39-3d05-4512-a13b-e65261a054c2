{"level":"INFO","timestamp":"2025-09-18T09:35:29.118229","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-18T09:35:29.120284","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-18T09:35:29.120317","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T09:35:29.120342","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T09:35:29.120361","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T09:35:29.120386","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.120405","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"a2c717d9-23c5-47a6-b10c-e658c4b965db"}}}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.193603","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.194728","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.195528","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.195575","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.195655","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.195670","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.195991","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196197","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196276","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196292","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196305","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196319","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196342","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196353","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196364","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196372","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196381","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196483","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196503","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196522","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196532","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196545","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196553","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-09-18T09:35:29.196656","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
