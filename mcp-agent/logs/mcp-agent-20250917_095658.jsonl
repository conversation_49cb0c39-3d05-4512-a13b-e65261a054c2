{"level":"INFO","timestamp":"2025-09-17T09:56:58.781897","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:56:58.783798","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:56:58.783829","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:56:58.783852","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:56:58.783870","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:56:58.783892","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.783912","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"b3c44650-f46c-4f95-816a-2acb638e97d1"}}}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.857285","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.858396","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.859237","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.859280","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.859357","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.859370","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.859669","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.859888","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.859977","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.859992","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.860004","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.860018","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.860032","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.860041","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.860055","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.860064","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.860077","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.860087","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.860102","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.860216","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.860228","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.860243","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.860251","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T09:56:58.860364","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.589770","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:57:01.589941","namespace":"mcp_agent.financial_websocket_demo_client_eee4a892","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:57:01.589966","namespace":"mcp_agent.financial_websocket_demo_client_eee4a892","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:57:01.589992","namespace":"mcp_agent.financial_websocket_demo_client_eee4a892","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:57:01.590008","namespace":"mcp_agent.financial_websocket_demo_client_eee4a892","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:57:01.590021","namespace":"mcp_agent.financial_websocket_demo_client_eee4a892","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.590041","namespace":"mcp_agent.financial_websocket_demo_client_eee4a892","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_eee4a892","agent_name":"mcp_application_loop","session_id":"4f071afb-d9a5-458d-b90b-d9541ef6a25b"}}}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.590071","namespace":"mcp_agent.financial_websocket_demo_client_eee4a892","message":"Financial session initialized for user client_eee4a892, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.593814","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_0bbb9e74"}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.593837","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.593847","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_0bbb9e74"}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.593987","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.600474","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.600503","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-17T09:57:01.601179","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_0bbb9e74.json"}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.601209","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_0bbb9e74"}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.601487","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_0bbb9e74"}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.601503","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_0bbb9e74"}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.601951","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_0bbb9e74"}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.601967","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.601990","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.602004","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.602016","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:57:01.602325","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:57:17.042661","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T09:57:24.606757","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T09:57:34.419726","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T09:57:45.484659","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.834088","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.834597","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_0bbb9e74.json"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.834637","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_0bbb9e74"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.834655","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 68.23s"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.834675","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.834695","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.834704","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.835150","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.846571","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.846969","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_0bbb9e74.json"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.846998","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_0bbb9e74"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.847013","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.847036","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.847050","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.847059","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.847388","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.851204","namespace":"mcp_agent.agents.agent","message":"Initializing agent alert_manager_websocketorchestration..."}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.852531","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Found server configuration=","data":{"data":{"name":"alert-notification","description":null,"transport":"sse","command":null,"args":[],"url":"http://localhost:6972/sse","headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.853067","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Up and running with a persistent connection!"}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.900293","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.905435","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":{},"logging":null,"prompts":{"listChanged":false},"resources":{"subscribe":"False","listChanged":"False"},"tools":{"listChanged":"False"},"completions":null},"serverInfo":{"name":"example-mcp-server","title":null,"version":"1.11.0"},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.905505","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.906638","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.914859","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"TestTool","title":null,"description":"Simple test tool.","inputSchema":{"properties":{"message":{"description":"Test message","title":"Message","type":"string"}},"required":["message"],"title":"TestToolArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"HttpNotification","title":null,"description":"Sends an HTTP alert to a specified endpoint.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the notification","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the notification message","title":"Content","type":"string"}},"required":["alert_message"],"title":"HttpNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"MqttNotification","title":null,"description":"Sends an MQTT alert to a specified topic.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the alert","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the alert message","title":"Content","type":"string"}},"required":["alert_message"],"title":"MqttNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"EmailNotification","title":null,"description":"Sends an email notification.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the email","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the email message","title":"Content","type":"string"}},"required":["alert_message"],"title":"EmailNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.915657","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"prompts/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.921033","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"prompts":[]}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.921500","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"resources/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.927286","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"resources":[]}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.927398","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"MCP Aggregator initialized for server 'alert-notification'","data":{"data":{"progress_action":"Initialized","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration","tool_count":4,"prompt_count":0,"resource_count":"0"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.927993","namespace":"mcp_agent.agents.agent","message":"Agent alert_manager_websocketorchestration initialized."}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.928736","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.929186","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.940239","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.941810","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-17T09:58:09.941899","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.941922","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.09s"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.941942","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_0bbb9e74 completed in 68.34s"}
{"level":"DEBUG","timestamp":"2025-09-17T09:58:09.942378","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_0bbb9e74.json"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.942419","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_0bbb9e74"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.942435","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_0bbb9e74 (success: True, time: 68.35s)"}
{"level":"INFO","timestamp":"2025-09-17T09:58:09.946608","namespace":"mcp_agent.financial_websocket_demo_client_eee4a892","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_eee4a892","agent_name":"mcp_application_loop"}}}
