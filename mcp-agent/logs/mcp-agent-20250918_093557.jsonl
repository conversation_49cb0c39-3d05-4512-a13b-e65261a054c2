{"level":"INFO","timestamp":"2025-09-18T09:35:57.589384","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-18T09:35:57.591490","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-18T09:35:57.591524","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T09:35:57.591552","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T09:35:57.591572","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T09:35:57.591592","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.591612","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"5710fbab-f0be-48c5-b6da-b3445eae78ac"}}}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.665910","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.667220","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.668064","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.668116","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.668200","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.668215","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.668544","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.668744","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.668845","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.668863","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.668878","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.668892","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.668911","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.668922","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.668932","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.668940","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.668950","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.669063","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.669082","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.669100","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.669111","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.669125","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.669133","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-09-18T09:35:57.669251","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-18T09:37:41.738404","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-18T09:37:41.738666","namespace":"mcp_agent.financial_websocket_mysql_test_client","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-18T09:37:41.738701","namespace":"mcp_agent.financial_websocket_mysql_test_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T09:37:41.738730","namespace":"mcp_agent.financial_websocket_mysql_test_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T09:37:41.738761","namespace":"mcp_agent.financial_websocket_mysql_test_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T09:37:41.738784","namespace":"mcp_agent.financial_websocket_mysql_test_client","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-18T09:37:41.738803","namespace":"mcp_agent.financial_websocket_mysql_test_client","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_mysql_test_client","agent_name":"mcp_application_loop","session_id":"e1c92e6b-4832-444f-9554-f8e2ff9717c0"}}}
{"level":"INFO","timestamp":"2025-09-18T09:37:41.740751","namespace":"mcp_agent.financial_websocket_mysql_test_client","message":"Financial session initialized for user test_client, type: mysql"}
{"level":"INFO","timestamp":"2025-09-18T09:39:04.812088","namespace":"mcp_agent.financial_websocket_mysql_test_client","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_mysql_test_client","agent_name":"mcp_application_loop"}}}
