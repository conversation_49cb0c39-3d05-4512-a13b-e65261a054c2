{"level":"INFO","timestamp":"2025-09-17T09:32:30.759419","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:32:30.761378","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:32:30.761409","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:32:30.761434","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:32:30.761454","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:32:30.761477","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.761498","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"9e9bc890-8598-4146-826b-a875cff152ab"}}}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.834302","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.835427","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.836280","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.836326","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.836404","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.836418","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.836767","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837035","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837129","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837150","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837168","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837180","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837194","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837202","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837216","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837225","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837239","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837250","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837264","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837380","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837393","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837407","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837416","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T09:32:30.837538","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.824622","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:32:34.824797","namespace":"mcp_agent.financial_websocket_demo_client_82e060d3","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:32:34.824822","namespace":"mcp_agent.financial_websocket_demo_client_82e060d3","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:32:34.824847","namespace":"mcp_agent.financial_websocket_demo_client_82e060d3","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:32:34.824862","namespace":"mcp_agent.financial_websocket_demo_client_82e060d3","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:32:34.824879","namespace":"mcp_agent.financial_websocket_demo_client_82e060d3","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.824895","namespace":"mcp_agent.financial_websocket_demo_client_82e060d3","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_82e060d3","agent_name":"mcp_application_loop","session_id":"aff7f903-36ab-4ca6-8452-55e88d24d1f9"}}}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.824925","namespace":"mcp_agent.financial_websocket_demo_client_82e060d3","message":"Financial session initialized for user client_82e060d3, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.828587","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_68b3a1ac"}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.828609","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.828619","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_68b3a1ac"}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.828636","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.835103","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.835131","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-17T09:32:34.835485","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_68b3a1ac.json"}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.835512","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_68b3a1ac"}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.835526","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_68b3a1ac"}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.835535","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_68b3a1ac"}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.835561","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_68b3a1ac"}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.835570","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.835592","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.835606","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.835618","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:32:34.835999","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:32:49.231644","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T09:33:07.229934","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T09:33:18.256172","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.350229","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.351292","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_68b3a1ac.json"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.351351","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_68b3a1ac"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.351375","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 100.51s"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.351445","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.351490","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.351505","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.352010","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.361386","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.361881","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_68b3a1ac.json"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.361911","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_68b3a1ac"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.361926","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.361949","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.361968","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.361977","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.362384","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.367268","namespace":"mcp_agent.agents.agent","message":"Initializing agent alert_manager_websocketorchestration..."}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.369910","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Found server configuration=","data":{"data":{"name":"alert-notification","description":null,"transport":"sse","command":null,"args":[],"url":"http://localhost:6972/sse","headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.370527","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Up and running with a persistent connection!"}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.413549","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.419077","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":{},"logging":null,"prompts":{"listChanged":false},"resources":{"subscribe":"False","listChanged":"False"},"tools":{"listChanged":"False"},"completions":null},"serverInfo":{"name":"example-mcp-server","title":null,"version":"1.11.0"},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.419218","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.420627","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.429431","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"TestTool","title":null,"description":"Simple test tool.","inputSchema":{"properties":{"message":{"description":"Test message","title":"Message","type":"string"}},"required":["message"],"title":"TestToolArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"HttpNotification","title":null,"description":"Sends an HTTP alert to a specified endpoint.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the notification","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the notification message","title":"Content","type":"string"}},"required":["alert_message"],"title":"HttpNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"MqttNotification","title":null,"description":"Sends an MQTT alert to a specified topic.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the alert","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the alert message","title":"Content","type":"string"}},"required":["alert_message"],"title":"MqttNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"EmailNotification","title":null,"description":"Sends an email notification.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the email","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the email message","title":"Content","type":"string"}},"required":["alert_message"],"title":"EmailNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.430532","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"prompts/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.435946","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"prompts":[]}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.436435","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"resources/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.441814","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"resources":[]}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.441914","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"MCP Aggregator initialized for server 'alert-notification'","data":{"data":{"progress_action":"Initialized","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration","tool_count":4,"prompt_count":0,"resource_count":"0"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.442418","namespace":"mcp_agent.agents.agent","message":"Agent alert_manager_websocketorchestration initialized."}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.443153","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.443593","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.454388","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.455992","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-17T09:34:15.456124","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.456147","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.09s"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.456165","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_68b3a1ac completed in 100.62s"}
{"level":"DEBUG","timestamp":"2025-09-17T09:34:15.456631","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_68b3a1ac.json"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.456660","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_68b3a1ac"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.456685","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_68b3a1ac (success: True, time: 100.63s)"}
{"level":"INFO","timestamp":"2025-09-17T09:34:15.461029","namespace":"mcp_agent.financial_websocket_demo_client_82e060d3","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_82e060d3","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.506145","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:42:13.506854","namespace":"mcp_agent.financial_websocket_demo_client_dd56b515","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:42:13.506924","namespace":"mcp_agent.financial_websocket_demo_client_dd56b515","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:42:13.506959","namespace":"mcp_agent.financial_websocket_demo_client_dd56b515","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:42:13.506986","namespace":"mcp_agent.financial_websocket_demo_client_dd56b515","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:42:13.507000","namespace":"mcp_agent.financial_websocket_demo_client_dd56b515","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.507038","namespace":"mcp_agent.financial_websocket_demo_client_dd56b515","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_dd56b515","agent_name":"mcp_application_loop","session_id":"7a047441-9d26-4e07-9473-d4da083dbbbe"}}}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.507176","namespace":"mcp_agent.financial_websocket_demo_client_dd56b515","message":"Financial session initialized for user client_dd56b515, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.513508","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_8092e8ca"}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.513533","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.513544","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_8092e8ca"}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.513591","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.514590","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.514609","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-17T09:42:13.515144","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_8092e8ca.json"}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.515173","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_8092e8ca"}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.515188","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_8092e8ca"}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.515202","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_8092e8ca"}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.515255","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_8092e8ca"}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.515266","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.515292","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.515309","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.515321","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:42:13.515718","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:42:30.979356","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-17T09:42:30.980115","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_8092e8ca.json"}
{"level":"INFO","timestamp":"2025-09-17T09:42:30.980155","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_8092e8ca"}
{"level":"INFO","timestamp":"2025-09-17T09:42:30.980174","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 17.46s"}
{"level":"INFO","timestamp":"2025-09-17T09:42:30.980193","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-17T09:42:30.980207","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-17T09:42:30.980216","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:42:30.980662","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:42:30.988339","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-17T09:42:30.988709","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_8092e8ca.json"}
{"level":"INFO","timestamp":"2025-09-17T09:42:30.988737","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_8092e8ca"}
{"level":"INFO","timestamp":"2025-09-17T09:42:30.988757","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-17T09:42:30.988773","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-17T09:42:30.988785","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-17T09:42:30.988794","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:42:30.989164","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:42:30.992437","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:42:30.993519","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:42:31.010927","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-17T09:42:31.012460","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-17T09:42:31.012535","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-17T09:42:31.012556","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.02s"}
{"level":"INFO","timestamp":"2025-09-17T09:42:31.012574","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_8092e8ca completed in 17.50s"}
{"level":"DEBUG","timestamp":"2025-09-17T09:42:31.012956","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_8092e8ca.json"}
{"level":"INFO","timestamp":"2025-09-17T09:42:31.012984","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_8092e8ca"}
{"level":"INFO","timestamp":"2025-09-17T09:42:31.013003","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_8092e8ca (success: True, time: 17.50s)"}
{"level":"INFO","timestamp":"2025-09-17T09:42:31.017008","namespace":"mcp_agent.financial_websocket_demo_client_dd56b515","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_dd56b515","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.792580","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:44:03.792873","namespace":"mcp_agent.financial_websocket_demo_client_da3ef708","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:44:03.792900","namespace":"mcp_agent.financial_websocket_demo_client_da3ef708","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:44:03.792920","namespace":"mcp_agent.financial_websocket_demo_client_da3ef708","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:44:03.792935","namespace":"mcp_agent.financial_websocket_demo_client_da3ef708","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:44:03.792948","namespace":"mcp_agent.financial_websocket_demo_client_da3ef708","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.792970","namespace":"mcp_agent.financial_websocket_demo_client_da3ef708","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_da3ef708","agent_name":"mcp_application_loop","session_id":"c7776014-9c8a-4077-9aba-4f3fdd6ec197"}}}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.793002","namespace":"mcp_agent.financial_websocket_demo_client_da3ef708","message":"Financial session initialized for user client_da3ef708, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.796475","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_d7651b05"}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.796498","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.796508","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_d7651b05"}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.796520","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.797264","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.797282","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-17T09:44:03.797683","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_d7651b05.json"}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.797710","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_d7651b05"}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.797724","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_d7651b05"}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.797734","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_d7651b05"}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.797760","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_d7651b05"}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.797770","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.797794","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.797807","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.797816","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:44:03.798182","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.624800","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-17T09:44:20.625374","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_d7651b05.json"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.625410","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_d7651b05"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.625429","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 16.83s"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.625462","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.625483","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.625493","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.625930","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.634904","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-17T09:44:20.635295","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_d7651b05.json"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.635327","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_d7651b05"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.635340","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.635355","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.635371","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.635380","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.635721","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.638794","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:44:20.639725","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:44:20.653042","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.654527","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-17T09:44:20.654609","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.654630","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.02s"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.654648","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_d7651b05 completed in 16.86s"}
{"level":"DEBUG","timestamp":"2025-09-17T09:44:20.655049","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_d7651b05.json"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.655077","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_d7651b05"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.655094","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_d7651b05 (success: True, time: 16.86s)"}
{"level":"INFO","timestamp":"2025-09-17T09:44:20.658958","namespace":"mcp_agent.financial_websocket_demo_client_da3ef708","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_da3ef708","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.969494","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:46:45.969798","namespace":"mcp_agent.financial_websocket_demo_client_11791e6a","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:46:45.969829","namespace":"mcp_agent.financial_websocket_demo_client_11791e6a","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:46:45.969851","namespace":"mcp_agent.financial_websocket_demo_client_11791e6a","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:46:45.969865","namespace":"mcp_agent.financial_websocket_demo_client_11791e6a","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:46:45.969878","namespace":"mcp_agent.financial_websocket_demo_client_11791e6a","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.969900","namespace":"mcp_agent.financial_websocket_demo_client_11791e6a","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_11791e6a","agent_name":"mcp_application_loop","session_id":"62537750-cdea-4feb-af20-f469ec4edefd"}}}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.969940","namespace":"mcp_agent.financial_websocket_demo_client_11791e6a","message":"Financial session initialized for user client_11791e6a, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.973379","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_828bdbc2"}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.973402","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.973412","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_828bdbc2"}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.973429","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.974217","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.974236","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-17T09:46:45.974598","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_828bdbc2.json"}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.974623","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_828bdbc2"}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.974639","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_828bdbc2"}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.974648","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_828bdbc2"}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.974668","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_828bdbc2"}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.974678","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.974701","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.974713","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.974723","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:46:45.975094","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.744952","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-17T09:47:01.745539","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_828bdbc2.json"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.745574","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_828bdbc2"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.745590","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 15.77s"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.745606","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.745620","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.745629","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.746048","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.753534","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-17T09:47:01.753892","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_828bdbc2.json"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.753921","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_828bdbc2"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.753934","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.753949","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.753961","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.753973","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.754322","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.757114","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:47:01.757821","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:47:01.770786","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.772241","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-17T09:47:01.772306","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.772326","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.02s"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.772342","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_828bdbc2 completed in 15.80s"}
{"level":"DEBUG","timestamp":"2025-09-17T09:47:01.772703","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_828bdbc2.json"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.772730","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_828bdbc2"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.772748","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_828bdbc2 (success: True, time: 15.80s)"}
{"level":"INFO","timestamp":"2025-09-17T09:47:01.776532","namespace":"mcp_agent.financial_websocket_demo_client_11791e6a","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_11791e6a","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.271057","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:32.271304","namespace":"mcp_agent.financial_websocket_demo_client_c66e73ad","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:32.271333","namespace":"mcp_agent.financial_websocket_demo_client_c66e73ad","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:32.271354","namespace":"mcp_agent.financial_websocket_demo_client_c66e73ad","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:32.271370","namespace":"mcp_agent.financial_websocket_demo_client_c66e73ad","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:32.271384","namespace":"mcp_agent.financial_websocket_demo_client_c66e73ad","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.271402","namespace":"mcp_agent.financial_websocket_demo_client_c66e73ad","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_c66e73ad","agent_name":"mcp_application_loop","session_id":"af4c788b-9519-4ec5-860b-6c1021f0707b"}}}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.271438","namespace":"mcp_agent.financial_websocket_demo_client_c66e73ad","message":"Financial session initialized for user client_c66e73ad, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.274856","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_8c8b4715"}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.274880","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.274891","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_8c8b4715"}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.274916","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.275719","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.275738","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:32.276105","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_8c8b4715.json"}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.276132","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_8c8b4715"}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.276148","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_8c8b4715"}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.276159","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_8c8b4715"}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.276196","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_8c8b4715"}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.276206","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.276234","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.276248","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.276259","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:48:32.276610","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.229630","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:47.230224","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_8c8b4715.json"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.230260","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_8c8b4715"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.230278","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 14.95s"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.230296","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.230309","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.230319","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.230731","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.238549","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:47.238922","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_8c8b4715.json"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.238951","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_8c8b4715"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.238964","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.238982","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.238995","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.239004","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.239349","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.242198","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:47.242815","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:47.255566","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.257029","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-17T09:48:47.257102","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.257122","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.02s"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.257139","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_8c8b4715 completed in 14.98s"}
{"level":"DEBUG","timestamp":"2025-09-17T09:48:47.257492","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_8c8b4715.json"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.257518","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_8c8b4715"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.257533","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_8c8b4715 (success: True, time: 14.98s)"}
{"level":"INFO","timestamp":"2025-09-17T09:48:47.261259","namespace":"mcp_agent.financial_websocket_demo_client_c66e73ad","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_c66e73ad","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.352759","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:51:03.353091","namespace":"mcp_agent.financial_websocket_demo_client_327ccdc0","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:51:03.353122","namespace":"mcp_agent.financial_websocket_demo_client_327ccdc0","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:51:03.353146","namespace":"mcp_agent.financial_websocket_demo_client_327ccdc0","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:51:03.353164","namespace":"mcp_agent.financial_websocket_demo_client_327ccdc0","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:51:03.353178","namespace":"mcp_agent.financial_websocket_demo_client_327ccdc0","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.353203","namespace":"mcp_agent.financial_websocket_demo_client_327ccdc0","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_327ccdc0","agent_name":"mcp_application_loop","session_id":"c26afa11-b041-4ecf-8c37-591570097a50"}}}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.353246","namespace":"mcp_agent.financial_websocket_demo_client_327ccdc0","message":"Financial session initialized for user client_327ccdc0, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.356909","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_33c08c21"}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.356934","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.356945","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_33c08c21"}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.356972","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.357802","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.357825","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-17T09:51:03.358284","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_33c08c21.json"}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.358311","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_33c08c21"}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.358327","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_33c08c21"}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.358337","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_33c08c21"}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.358370","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_33c08c21"}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.358379","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.358408","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.358424","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.358434","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:51:03.358841","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.905845","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-17T09:51:22.906292","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_33c08c21.json"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.906334","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_33c08c21"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.906350","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 19.55s"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.906372","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.906384","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.906394","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.906821","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.914047","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-17T09:51:22.914422","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_33c08c21.json"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.914457","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_33c08c21"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.914472","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.914487","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.914502","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.914512","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.914892","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.919192","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:51:22.919945","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:51:22.932890","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.934351","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-17T09:51:22.934423","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.934442","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.02s"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.934459","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_33c08c21 completed in 19.58s"}
{"level":"DEBUG","timestamp":"2025-09-17T09:51:22.934818","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_33c08c21.json"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.934853","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_33c08c21"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.934869","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_33c08c21 (success: True, time: 19.58s)"}
{"level":"INFO","timestamp":"2025-09-17T09:51:22.938661","namespace":"mcp_agent.financial_websocket_demo_client_327ccdc0","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_327ccdc0","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.445119","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:55:51.445498","namespace":"mcp_agent.financial_websocket_demo_client_7db9096f","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:55:51.445530","namespace":"mcp_agent.financial_websocket_demo_client_7db9096f","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:55:51.445551","namespace":"mcp_agent.financial_websocket_demo_client_7db9096f","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:55:51.445567","namespace":"mcp_agent.financial_websocket_demo_client_7db9096f","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:55:51.445581","namespace":"mcp_agent.financial_websocket_demo_client_7db9096f","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.445604","namespace":"mcp_agent.financial_websocket_demo_client_7db9096f","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_7db9096f","agent_name":"mcp_application_loop","session_id":"61900511-88f0-4008-8440-b3f255096946"}}}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.445644","namespace":"mcp_agent.financial_websocket_demo_client_7db9096f","message":"Financial session initialized for user client_7db9096f, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.449242","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_c884b71b"}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.449265","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.449275","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_c884b71b"}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.449301","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.450135","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.450152","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-17T09:55:51.450549","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_c884b71b.json"}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.450574","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_c884b71b"}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.450587","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_c884b71b"}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.450598","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_c884b71b"}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.450633","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_c884b71b"}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.450643","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.450678","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.450694","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.450703","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:55:51.451103","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.054651","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-17T09:56:06.056133","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_c884b71b.json"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.056174","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_c884b71b"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.056191","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 14.60s"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.056211","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.056227","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.056237","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.056639","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.064230","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-17T09:56:06.064586","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_c884b71b.json"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.064620","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_c884b71b"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.064637","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.064653","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.064664","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.064674","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.065005","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.067837","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:56:06.068571","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:56:06.082163","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.083624","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-17T09:56:06.083698","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.083717","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.02s"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.083734","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_c884b71b completed in 14.63s"}
{"level":"DEBUG","timestamp":"2025-09-17T09:56:06.084093","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_c884b71b.json"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.084129","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_c884b71b"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.084150","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_c884b71b (success: True, time: 14.63s)"}
{"level":"INFO","timestamp":"2025-09-17T09:56:06.087963","namespace":"mcp_agent.financial_websocket_demo_client_7db9096f","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_7db9096f","agent_name":"mcp_application_loop"}}}
