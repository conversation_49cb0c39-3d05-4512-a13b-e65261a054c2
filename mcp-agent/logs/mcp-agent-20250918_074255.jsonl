{"level":"INFO","timestamp":"2025-09-18T07:42:55.407335","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-18T07:42:55.409322","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-18T07:42:55.409352","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:42:55.409376","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:42:55.409394","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:42:55.409418","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.409435","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"ac4b7bcd-0bab-4ec7-b0e2-a28cb4fcb945"}}}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.482237","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.483326","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.484157","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.484201","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.484274","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.484287","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.484585","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.484796","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.484880","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.484895","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.484912","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.484926","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.484942","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.484951","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.484965","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.484973","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.484983","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.485088","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.485106","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.485125","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.485135","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.485149","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.485157","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-09-18T07:42:55.485267","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.956905","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-18T07:43:09.957079","namespace":"mcp_agent.financial_websocket_orchestration_demo_shortage","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-18T07:43:09.957104","namespace":"mcp_agent.financial_websocket_orchestration_demo_shortage","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:43:09.957127","namespace":"mcp_agent.financial_websocket_orchestration_demo_shortage","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:43:09.957141","namespace":"mcp_agent.financial_websocket_orchestration_demo_shortage","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:43:09.957156","namespace":"mcp_agent.financial_websocket_orchestration_demo_shortage","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.957172","namespace":"mcp_agent.financial_websocket_orchestration_demo_shortage","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_orchestration_demo_shortage","agent_name":"mcp_application_loop","session_id":"557a7346-7ecb-40e1-99c5-649317ec9325"}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:43:09.957301","namespace":"mcp_agent.agents.agent","message":"Initializing agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-09-18T07:43:09.957946","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator initialized."}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.957980","namespace":"mcp_agent.financial_websocket_orchestration_demo_shortage","message":"Financial session initialized for user demo_shortage, type: orchestration"}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.961370","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_28dd07c5"}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.961393","namespace":"orchestrator.orchestration_runner","message":"Query: Trigger the orchestrated shortage workflow: query MySQL for customer order CUSTORD-202506001 from Tech Pioneer Co., Ltd. The order needs 4000 units of GPU MM2004IC001, 2000 units of CPU DEP2004IC001, and 8000 units of memory ATR6G00801. Produce shortage indices and escalate a critical alert with urgent notification if inventory is insufficient."}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.961404","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_28dd07c5"}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.961546","namespace":"orchestrator.query_processor","message":"Processing financial query: Trigger the orchestrated shortage workflow: query MySQL for customer order CUSTORD-202506001 from Te..."}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.968144","namespace":"orchestrator.query_processor","message":"Query classified as shortage_analysis (confidence: 0.17, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.968171","namespace":"orchestrator.orchestration_runner","message":"Query parsed as shortage_analysis (confidence: 0.17)"}
{"level":"DEBUG","timestamp":"2025-09-18T07:43:09.968846","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_28dd07c5.json"}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.968876","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_28dd07c5"}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.970422","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_28dd07c5"}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.970446","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_28dd07c5"}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.970947","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_28dd07c5"}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.970965","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.970989","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.971003","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.971014","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-18T07:43:09.971326","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-18T07:43:26.076339","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-18T07:43:33.952356","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-18T07:43:44.492957","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.518335","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.518855","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_28dd07c5.json"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.518899","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_28dd07c5"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.518923","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 52.55s"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.518944","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.518964","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.518974","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.519469","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.531937","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.532349","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_28dd07c5.json"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.532385","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_28dd07c5"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.532401","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.532418","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.532542","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.532553","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.532901","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.536653","namespace":"mcp_agent.agents.agent","message":"Initializing agent alert_manager_websocketorchestration..."}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.537636","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Found server configuration=","data":{"data":{"name":"alert-notification","description":null,"transport":"sse","command":null,"args":[],"url":"http://localhost:6972/sse","headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.538159","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Up and running with a persistent connection!"}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.587823","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0"}}}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.593070","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":{},"logging":null,"prompts":{"listChanged":false},"resources":{"subscribe":"False","listChanged":"False"},"tools":{"listChanged":"False"},"completions":null},"serverInfo":{"name":"example-mcp-server","title":null,"version":"1.11.0"},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.593144","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.594254","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.606070","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"TestTool","title":null,"description":"Simple test tool.","inputSchema":{"properties":{"message":{"description":"Test message","title":"Message","type":"string"}},"required":["message"],"title":"TestToolArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"HttpNotification","title":null,"description":"Sends an HTTP alert to a specified endpoint.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the notification","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the notification message","title":"Content","type":"string"}},"required":["alert_message"],"title":"HttpNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"MqttNotification","title":null,"description":"Sends an MQTT alert to a specified topic.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the alert","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the alert message","title":"Content","type":"string"}},"required":["alert_message"],"title":"MqttNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"EmailNotification","title":null,"description":"Sends an email notification.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the email","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the email message","title":"Content","type":"string"}},"required":["alert_message"],"title":"EmailNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.606883","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"prompts/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.612478","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"prompts":[]}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.612955","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"resources/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.618513","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"resources":[]}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.618588","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"MCP Aggregator initialized for server 'alert-notification'","data":{"data":{"progress_action":"Initialized","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration","tool_count":4,"prompt_count":0,"resource_count":"0"}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.619042","namespace":"mcp_agent.agents.agent","message":"Agent alert_manager_websocketorchestration initialized."}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.619702","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.620119","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.633590","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.635081","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-18T07:44:02.635160","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.635181","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.10s"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.635199","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_28dd07c5 completed in 52.66s"}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.635593","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_28dd07c5.json"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.635626","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_28dd07c5"}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.635646","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_28dd07c5 (success: True, time: 52.67s)"}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.639112","namespace":"mcp_agent.agents.agent","message":"Shutting down agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.639295","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Decremented connection ref count to 1"}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.639314","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Aggregator closing with ref count 1, connection manager will remain active"}
{"level":"DEBUG","timestamp":"2025-09-18T07:44:02.639346","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator shutdown."}
{"level":"INFO","timestamp":"2025-09-18T07:44:02.639378","namespace":"mcp_agent.financial_websocket_orchestration_demo_shortage","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_orchestration_demo_shortage","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.816228","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:29.816558","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:29.816594","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:29.816618","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:29.816637","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:29.816655","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.816679","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_orchestration_test_shortage","agent_name":"mcp_application_loop","session_id":"c43cbc31-ffe0-4706-aa39-bee46c8d8036"}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:29.816956","namespace":"mcp_agent.agents.agent","message":"Initializing agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:29.817548","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator initialized."}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.817581","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage","message":"Financial session initialized for user test_shortage, type: orchestration"}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.822515","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_5267707d"}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.822539","namespace":"orchestrator.orchestration_runner","message":"Query: Trigger the orchestrated shortage workflow: query MySQL for customer order CUSTORD-202506001 from Tech Pioneer Co., Ltd. The order needs 4000 units of GPU MM2004IC001, 2000 units of CPU DEP2004IC001, and 8000 units of memory ATR6G00801. Produce shortage indices and escalate a critical alert with urgent notification if inventory is insufficient."}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.822557","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_5267707d"}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.822709","namespace":"orchestrator.query_processor","message":"Processing financial query: Trigger the orchestrated shortage workflow: query MySQL for customer order CUSTORD-202506001 from Te..."}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.823677","namespace":"orchestrator.query_processor","message":"Query classified as shortage_analysis (confidence: 0.17, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.823695","namespace":"orchestrator.orchestration_runner","message":"Query parsed as shortage_analysis (confidence: 0.17)"}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:29.824364","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_5267707d.json"}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.824398","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_5267707d"}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.824701","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_5267707d"}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.824717","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_5267707d"}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.825216","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_5267707d"}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.825233","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.825265","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.825280","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.825291","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-18T07:52:29.825628","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.497063","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:45.497750","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_5267707d.json"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.497799","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_5267707d"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.497824","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 15.67s"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.497843","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.497860","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.497870","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.498282","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.510482","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:45.510884","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_5267707d.json"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.510921","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_5267707d"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.510936","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.510952","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.510975","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.510985","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.511344","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.514723","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:45.515795","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:45.530990","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.532488","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-18T07:52:45.532579","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.532599","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.02s"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.532617","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_5267707d completed in 15.71s"}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:45.533015","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_5267707d.json"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.533048","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_5267707d"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.533068","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_5267707d (success: True, time: 15.71s)"}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:45.536510","namespace":"mcp_agent.agents.agent","message":"Shutting down agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:45.536709","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Decremented connection ref count to 0"}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.536728","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Last aggregator closing, shutting down all persistent connections..."}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.536770","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"Disconnecting all persistent server connections..."}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:45.537183","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"MCPConnectionManager: shutting down all server tasks..."}
{"level":"INFO","timestamp":"2025-09-18T07:52:45.537202","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"Disconnecting all persistent server connections..."}
{"level":"INFO","timestamp":"2025-09-18T07:52:46.037717","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Connection manager successfully closed and removed from context"}
{"level":"DEBUG","timestamp":"2025-09-18T07:52:46.037803","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator shutdown."}
{"level":"INFO","timestamp":"2025-09-18T07:52:46.037847","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_orchestration_test_shortage","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.315291","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:05.315501","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage2","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:05.315525","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage2","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:05.315543","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage2","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:05.315558","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage2","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:05.315572","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage2","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.315593","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage2","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_orchestration_test_shortage2","agent_name":"mcp_application_loop","session_id":"b3d278a1-a870-4b10-a298-f73e0c3f6181"}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:05.315716","namespace":"mcp_agent.agents.agent","message":"Initializing agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:05.316084","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator initialized."}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.316111","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage2","message":"Financial session initialized for user test_shortage2, type: orchestration"}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.319169","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_486edc13"}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.319192","namespace":"orchestrator.orchestration_runner","message":"Query: Trigger the orchestrated shortage workflow: query MySQL for customer order CUSTORD-202506001 from Tech Pioneer Co., Ltd. The order needs 4000 units of GPU MM2004IC001, 2000 units of CPU DEP2004IC001, and 8000 units of memory ATR6G00801. Produce shortage indices and escalate a critical alert with urgent notification if inventory is insufficient."}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.319203","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_486edc13"}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.319336","namespace":"orchestrator.query_processor","message":"Processing financial query: Trigger the orchestrated shortage workflow: query MySQL for customer order CUSTORD-202506001 from Te..."}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.320189","namespace":"orchestrator.query_processor","message":"Query classified as shortage_analysis (confidence: 0.17, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.320207","namespace":"orchestrator.orchestration_runner","message":"Query parsed as shortage_analysis (confidence: 0.17)"}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:05.320806","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_486edc13.json"}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.320835","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_486edc13"}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.321099","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_486edc13"}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.321115","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_486edc13"}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.321566","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_486edc13"}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.321581","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.321602","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.321614","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.321624","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-18T07:54:05.321934","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.277385","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:24.277836","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_486edc13.json"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.277869","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_486edc13"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.277886","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 18.96s"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.277903","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.277917","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.277926","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.278352","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.291372","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:24.291766","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_486edc13.json"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.291795","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_486edc13"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.291809","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.291825","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.291839","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.291848","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.292164","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.296481","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:24.297094","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:24.309749","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.311220","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-18T07:54:24.311284","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.311304","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.02s"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.311322","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_486edc13 completed in 18.99s"}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:24.311703","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_486edc13.json"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.311729","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_486edc13"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.311743","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_486edc13 (success: True, time: 18.99s)"}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:24.315072","namespace":"mcp_agent.agents.agent","message":"Shutting down agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:24.315251","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Decremented connection ref count to 0"}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.315270","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Last aggregator closing, shutting down all persistent connections..."}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.315298","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"Disconnecting all persistent server connections..."}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:24.315661","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"MCPConnectionManager: shutting down all server tasks..."}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.315679","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"Disconnecting all persistent server connections..."}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.816383","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Connection manager successfully closed and removed from context"}
{"level":"DEBUG","timestamp":"2025-09-18T07:54:24.816462","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator shutdown."}
{"level":"INFO","timestamp":"2025-09-18T07:54:24.816492","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage2","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_orchestration_test_shortage2","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.191535","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:06.191703","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage3","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:06.191726","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage3","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:06.191745","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage3","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:06.191766","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage3","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:06.191780","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage3","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.191797","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage3","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_orchestration_test_shortage3","agent_name":"mcp_application_loop","session_id":"907d6e45-a32b-43f6-ac4f-482b7b43e114"}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:06.191902","namespace":"mcp_agent.agents.agent","message":"Initializing agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:06.192266","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator initialized."}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.192293","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage3","message":"Financial session initialized for user test_shortage3, type: orchestration"}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.195268","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_26d7efae"}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.195291","namespace":"orchestrator.orchestration_runner","message":"Query: Trigger the orchestrated shortage workflow: query MySQL for customer order CUSTORD-202506001 from Tech Pioneer Co., Ltd. The order needs 4000 units of GPU MM2004IC001, 2000 units of CPU DEP2004IC001, and 8000 units of memory ATR6G00801. Produce shortage indices and escalate a critical alert with urgent notification if inventory is insufficient."}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.195302","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_26d7efae"}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.195427","namespace":"orchestrator.query_processor","message":"Processing financial query: Trigger the orchestrated shortage workflow: query MySQL for customer order CUSTORD-202506001 from Te..."}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.196239","namespace":"orchestrator.query_processor","message":"Query classified as shortage_analysis (confidence: 0.17, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.196258","namespace":"orchestrator.orchestration_runner","message":"Query parsed as shortage_analysis (confidence: 0.17)"}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:06.196833","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_26d7efae.json"}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.196862","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_26d7efae"}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.197127","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_26d7efae"}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.197144","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_26d7efae"}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.197586","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_26d7efae"}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.197602","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.197624","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.197637","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.197646","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-18T07:55:06.197951","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.384347","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:28.384792","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_26d7efae.json"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.384825","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_26d7efae"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.384842","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 22.19s"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.384860","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.384874","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.384884","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.385292","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.410307","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:28.410696","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_26d7efae.json"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.410725","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_26d7efae"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.410740","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.03s"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.410768","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.410781","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.410790","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.411107","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.413818","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:28.414740","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:28.432918","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.434390","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-18T07:55:28.434469","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.434491","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.02s"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.434508","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_26d7efae completed in 22.24s"}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:28.434901","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_26d7efae.json"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.434928","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_26d7efae"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.434943","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_26d7efae (success: True, time: 22.24s)"}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:28.438275","namespace":"mcp_agent.agents.agent","message":"Shutting down agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:28.438461","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Decremented connection ref count to 0"}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.438477","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Last aggregator closing, shutting down all persistent connections..."}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.438501","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"Disconnecting all persistent server connections..."}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:28.438866","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"MCPConnectionManager: shutting down all server tasks..."}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.438885","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"Disconnecting all persistent server connections..."}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.939516","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Connection manager successfully closed and removed from context"}
{"level":"DEBUG","timestamp":"2025-09-18T07:55:28.939614","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator shutdown."}
{"level":"INFO","timestamp":"2025-09-18T07:55:28.939646","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage3","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_orchestration_test_shortage3","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.613524","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:29.614549","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage4","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:29.614614","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage4","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:29.614649","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage4","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:29.614673","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage4","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:29.614704","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage4","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.614765","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage4","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_orchestration_test_shortage4","agent_name":"mcp_application_loop","session_id":"784a62cc-9175-4753-bc2e-7d4ef4c0f668"}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:29.615511","namespace":"mcp_agent.agents.agent","message":"Initializing agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:29.616809","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator initialized."}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.616849","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage4","message":"Financial session initialized for user test_shortage4, type: orchestration"}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.622516","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_034a30af"}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.622547","namespace":"orchestrator.orchestration_runner","message":"Query: Trigger the orchestrated shortage workflow: query MySQL for customer order CUSTORD-202506001 from Tech Pioneer Co., Ltd. The order needs 4000 units of GPU MM2004IC001, 2000 units of CPU DEP2004IC001, and 8000 units of memory ATR6G00801. Produce shortage indices and escalate a critical alert with urgent notification if inventory is insufficient."}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.622561","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_034a30af"}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.622786","namespace":"orchestrator.query_processor","message":"Processing financial query: Trigger the orchestrated shortage workflow: query MySQL for customer order CUSTORD-202506001 from Te..."}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.624655","namespace":"orchestrator.query_processor","message":"Query classified as shortage_analysis (confidence: 0.17, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.624680","namespace":"orchestrator.orchestration_runner","message":"Query parsed as shortage_analysis (confidence: 0.17)"}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:29.626297","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_034a30af.json"}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.626355","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_034a30af"}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.626789","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_034a30af"}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.626808","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_034a30af"}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.627317","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_034a30af"}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.627335","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.627371","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.627388","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.627399","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-18T07:59:29.627790","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.640026","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:48.640605","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_034a30af.json"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.640639","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_034a30af"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.640655","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 19.01s"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.640682","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.640697","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.640708","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.641139","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.670940","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:48.671353","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_034a30af.json"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.671382","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_034a30af"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.671397","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.03s"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.671423","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.671436","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.671446","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.671842","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.675125","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:48.676222","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:48.690297","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.691867","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-18T07:59:48.691955","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.691976","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.02s"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.691995","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_034a30af completed in 19.06s"}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:48.692423","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_034a30af.json"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.692451","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_034a30af"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.692466","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_034a30af (success: True, time: 19.07s)"}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:48.695931","namespace":"mcp_agent.agents.agent","message":"Shutting down agent orchestration_coordinator..."}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:48.696198","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Decremented connection ref count to 0"}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.696216","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Last aggregator closing, shutting down all persistent connections..."}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.696268","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"Disconnecting all persistent server connections..."}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:48.696814","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"MCPConnectionManager: shutting down all server tasks..."}
{"level":"INFO","timestamp":"2025-09-18T07:59:48.696844","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"Disconnecting all persistent server connections..."}
{"level":"INFO","timestamp":"2025-09-18T07:59:49.197156","namespace":"mcp_agent.mcp.mcp_aggregator.orchestration_coordinator","message":"Connection manager successfully closed and removed from context"}
{"level":"DEBUG","timestamp":"2025-09-18T07:59:49.197220","namespace":"mcp_agent.agents.agent","message":"Agent orchestration_coordinator shutdown."}
{"level":"INFO","timestamp":"2025-09-18T07:59:49.197268","namespace":"mcp_agent.financial_websocket_orchestration_test_shortage4","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_orchestration_test_shortage4","agent_name":"mcp_application_loop"}}}
