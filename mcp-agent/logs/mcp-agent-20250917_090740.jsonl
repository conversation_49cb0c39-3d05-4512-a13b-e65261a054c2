{"level":"INFO","timestamp":"2025-09-17T09:07:40.651813","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:07:40.653819","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:07:40.653850","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:07:40.653874","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:07:40.653893","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:07:40.653915","namespace":"mcp_agent.websocket_financial_analyzer","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.653934","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop","session_id":"d5f61a12-9c57-487a-9071-a3be346c750f"}}}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.726761","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.enhanced_shortage_analyzer_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.727904","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.alert_manager_websocketorchestration","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.728734","namespace":"orchestrator.context_manager","message":"ContextManager initialized with persistence: True"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.728787","namespace":"orchestrator.query_processor","message":"FinancialQueryProcessor initialized"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.728864","namespace":"orchestrator.workflow_patterns","message":"WorkflowPatternRegistry initialized with 5 patterns"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.728877","namespace":"orchestrator.workflow_patterns","message":"WorkflowExecutor initialized"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729185","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Planner","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729388","namespace":"mcp_agent.workflows.llm.augmented_llm_vllm.LLM Orchestration Synthesizer","message":"Using vLLM API at http://192.168.1.54:38701/v1"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729472","namespace":"orchestrator.financial_orchestrator","message":"FinancialOrchestrator 'MainFinancialOrchestrator' initialized with 3 agents"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729488","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner initialized with all components"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729501","namespace":"orchestrator.orchestration_runner","message":"OrchestrationRunner created via factory function"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729514","namespace":"agents.agent_interfaces","message":"AgentOrchestrationManager initialized"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729528","namespace":"agents.agent_interfaces","message":"MySQLAgentInterface created for mysql_analyzer"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729537","namespace":"agents.agent_interfaces","message":"Registered agent mysql_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729553","namespace":"agents.agent_interfaces","message":"ShortageAgentInterface created for shortage_analyzer"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729562","namespace":"agents.agent_interfaces","message":"Registered agent shortage_analyzer with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729577","namespace":"agents.agent_interfaces","message":"AlertAgentInterface created for alert_manager"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729586","namespace":"agents.agent_interfaces","message":"Registered agent alert_manager with orchestration interface"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729601","namespace":"agents.agent_interfaces","message":"Agent mysql_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729715","namespace":"agents.agent_interfaces","message":"Shortage agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729726","namespace":"agents.agent_interfaces","message":"Agent shortage_analyzer initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729741","namespace":"agents.agent_interfaces","message":"Alert agent initialized for orchestration"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729750","namespace":"agents.agent_interfaces","message":"Agent alert_manager initialization: success"}
{"level":"INFO","timestamp":"2025-09-17T09:07:40.729865","namespace":"mcp_agent.websocket_financial_analyzer","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"websocket_financial_analyzer","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.861932","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:07:43.862124","namespace":"mcp_agent.financial_websocket_demo_client_35110088","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:07:43.862149","namespace":"mcp_agent.financial_websocket_demo_client_35110088","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:07:43.862175","namespace":"mcp_agent.financial_websocket_demo_client_35110088","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:07:43.862190","namespace":"mcp_agent.financial_websocket_demo_client_35110088","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:07:43.862203","namespace":"mcp_agent.financial_websocket_demo_client_35110088","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.862222","namespace":"mcp_agent.financial_websocket_demo_client_35110088","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_35110088","agent_name":"mcp_application_loop","session_id":"272e9084-b0c0-46e5-86ce-e9aa48ef1745"}}}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.862254","namespace":"mcp_agent.financial_websocket_demo_client_35110088","message":"Financial session initialized for user client_35110088, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.866157","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_c7d900a4"}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.866182","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.866193","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_c7d900a4"}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.866210","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.872759","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.872787","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-17T09:07:43.873145","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_c7d900a4.json"}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.873171","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_c7d900a4"}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.873185","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_c7d900a4"}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.873194","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_c7d900a4"}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.873219","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_c7d900a4"}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.873228","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.873250","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.873264","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.873278","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:07:43.873664","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:08:00.907568","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T09:08:09.077905","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T09:08:20.592590","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_progress event for mysql_agent"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.339703","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.340207","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_c7d900a4.json"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.340242","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_c7d900a4"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.340259","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 64.47s"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.340281","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.340301","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.340311","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.340742","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.351544","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.352023","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_c7d900a4.json"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.352065","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_c7d900a4"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.352080","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.352098","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.352112","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.352122","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.352497","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.356393","namespace":"mcp_agent.agents.agent","message":"Initializing agent alert_manager_websocketorchestration..."}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.358263","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Found server configuration=","data":{"data":{"name":"alert-notification","description":null,"transport":"sse","command":null,"args":[],"url":"http://localhost:6972/sse","headers":null,"http_timeout_seconds":null,"read_timeout_seconds":null,"terminate_on_close":true,"auth":null,"roots":null,"env":null}}}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.358831","namespace":"mcp_agent.mcp.mcp_connection_manager","message":"alert-notification: Up and running with a persistent connection!"}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.399543","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"initialize","params":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":null,"sampling":{},"elicitation":{},"roots":{"listChanged":true}},"clientInfo":{"name":"mcp","title":null,"version":"0.1.0"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.404648","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"protocolVersion":"2025-06-18","capabilities":{"experimental":{},"logging":null,"prompts":{"listChanged":false},"resources":{"subscribe":"False","listChanged":"False"},"tools":{"listChanged":"False"},"completions":null},"serverInfo":{"name":"example-mcp-server","title":null,"version":"1.11.0"},"instructions":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.404721","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_notification:","data":{"data":{"method":"notifications/initialized","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.405895","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.413937","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"tools":[{"name":"TestTool","title":null,"description":"Simple test tool.","inputSchema":{"properties":{"message":{"description":"Test message","title":"Message","type":"string"}},"required":["message"],"title":"TestToolArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"HttpNotification","title":null,"description":"Sends an HTTP alert to a specified endpoint.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the notification","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the notification message","title":"Content","type":"string"}},"required":["alert_message"],"title":"HttpNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"MqttNotification","title":null,"description":"Sends an MQTT alert to a specified topic.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the alert","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the alert message","title":"Content","type":"string"}},"required":["alert_message"],"title":"MqttNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null},{"name":"EmailNotification","title":null,"description":"Sends an email notification.","inputSchema":{"properties":{"alert_message":{"description":"Alert message from Alert Manager","title":"Alert Message","type":"string"},"subject":{"default":null,"description":"The subject of the email","title":"Subject","type":"string"},"content":{"default":null,"description":"The content of the email message","title":"Content","type":"string"}},"required":["alert_message"],"title":"EmailNotificationArguments","type":"object"},"outputSchema":null,"annotations":null,"meta":null}]}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.414714","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"prompts/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.420156","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"prompts":[]}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.420775","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"resources/list","params":null}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.426264","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"nextCursor":null,"resources":[]}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.426339","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"MCP Aggregator initialized for server 'alert-notification'","data":{"data":{"progress_action":"Initialized","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration","tool_count":4,"prompt_count":0,"resource_count":"0"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.426794","namespace":"mcp_agent.agents.agent","message":"Agent alert_manager_websocketorchestration initialized."}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.427449","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.427858","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.438735","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.439969","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-17T09:08:48.440049","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.440070","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.09s"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.440089","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_c7d900a4 completed in 64.57s"}
{"level":"DEBUG","timestamp":"2025-09-17T09:08:48.440480","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_c7d900a4.json"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.440517","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_c7d900a4"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.440532","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_c7d900a4 (success: True, time: 64.57s)"}
{"level":"INFO","timestamp":"2025-09-17T09:08:48.442226","namespace":"mcp_agent.financial_websocket_demo_client_35110088","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_35110088","agent_name":"mcp_application_loop"}}}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.578874","namespace":"mcp_agent.core.context","message":"Configuring logger with level: debug"}
{"level":"DEBUG","timestamp":"2025-09-17T09:18:36.579633","namespace":"mcp_agent.financial_websocket_demo_client_9c0d7821","message":"Registering global workflow tasks with application instance."}
{"level":"DEBUG","timestamp":"2025-09-17T09:18:36.579675","namespace":"mcp_agent.financial_websocket_demo_client_9c0d7821","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:18:36.579701","namespace":"mcp_agent.financial_websocket_demo_client_9c0d7821","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_openai.OpenAICompletionTasks.request_structured_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:18:36.579720","namespace":"mcp_agent.financial_websocket_demo_client_9c0d7821","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_completion_task"}
{"level":"DEBUG","timestamp":"2025-09-17T09:18:36.579734","namespace":"mcp_agent.financial_websocket_demo_client_9c0d7821","message":"Registering global workflow task: mcp_agent.workflows.llm.augmented_llm_vllm.VLLMCompletionTasks.request_structured_completion_task"}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.579781","namespace":"mcp_agent.financial_websocket_demo_client_9c0d7821","message":"MCPApp initialized","data":{"data":{"progress_action":"Running","target":"financial_websocket_demo_client_9c0d7821","agent_name":"mcp_application_loop","session_id":"681097e8-416e-4f78-a903-5b2c8e1c8950"}}}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.579918","namespace":"mcp_agent.financial_websocket_demo_client_9c0d7821","message":"Financial session initialized for user client_9c0d7821, type: demo"}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.584898","namespace":"orchestrator.orchestration_runner","message":"Starting financial query execution: workflow_d2398041"}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.584923","namespace":"orchestrator.orchestration_runner","message":"Query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units), DEP2004IC001 CPUs (2000 units), ATR6G00801 Memory (8000 units). Calculate shortage index and IMMEDIATELY NOTIFY customer Tech Pioneer Co., Ltd. of delivery risks via URGENT ESCALATION."}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.584934","namespace":"orchestrator.orchestration_runner","message":"Tool streaming callback provided for workflow workflow_d2398041"}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.584983","namespace":"orchestrator.query_processor","message":"Processing financial query: CRITICAL SHORTAGE ALERT: Check database for CUSTORD-202506001 requiring MM2004IC001 GPUs (4000 units..."}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.585948","namespace":"orchestrator.query_processor","message":"Query classified as customer_priority (confidence: 0.10, workflow: full_workflow)"}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.585968","namespace":"orchestrator.orchestration_runner","message":"Query parsed as customer_priority (confidence: 0.10)"}
{"level":"DEBUG","timestamp":"2025-09-17T09:18:36.586466","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_d2398041.json"}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.586497","namespace":"orchestrator.context_manager","message":"Created new context for workflow workflow_d2398041"}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.586511","namespace":"orchestrator.orchestration_runner","message":"Executing pattern-based workflow for workflow_d2398041"}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.586523","namespace":"orchestrator.orchestration_runner","message":"Pattern-based execution: Tool streaming callback available for workflow_d2398041"}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.586579","namespace":"orchestrator.workflow_patterns","message":"Executing workflow pattern shortage_analysis for workflow workflow_d2398041"}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.586593","namespace":"orchestrator.workflow_patterns","message":"Workflow executor: Tool streaming callback available for pattern shortage_analysis"}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.586625","namespace":"orchestrator.workflow_patterns","message":"Executing step group 1: ['mysql_data_analysis']"}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.586644","namespace":"orchestrator.workflow_patterns","message":"Executing step mysql_data_analysis: Analyze historical data and current inventory levels"}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.586660","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:18:36.587076","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.502593","namespace":"orchestrator.workflow_patterns","message":"MySQL step: Sent tool_call_result event"}
{"level":"DEBUG","timestamp":"2025-09-17T09:18:55.503125","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_d2398041.json"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.503173","namespace":"orchestrator.context_manager","message":"Updated MySQL context for workflow workflow_d2398041"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.503200","namespace":"orchestrator.workflow_patterns","message":"Step mysql_data_analysis completed successfully in 18.92s"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.503233","namespace":"orchestrator.workflow_patterns","message":"Executing step group 2: ['shortage_calculation']"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.503253","namespace":"orchestrator.workflow_patterns","message":"Executing step shortage_calculation: Calculate shortage indices and risk levels"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.503263","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.503737","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.512149","namespace":"orchestrator.workflow_patterns","message":"Shortage step: Sent tool_call_result events"}
{"level":"DEBUG","timestamp":"2025-09-17T09:18:55.512624","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_d2398041.json"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.512656","namespace":"orchestrator.context_manager","message":"Updated shortage context for workflow workflow_d2398041"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.512671","namespace":"orchestrator.workflow_patterns","message":"Step shortage_calculation completed successfully in 0.01s"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.512688","namespace":"orchestrator.workflow_patterns","message":"Executing step group 3: ['alert_processing']"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.512704","namespace":"orchestrator.workflow_patterns","message":"Executing step alert_processing: Process shortage results and send notifications"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.512716","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing: Tool streaming callback available"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.513105","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent HttpNotification tool_call_start event"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.516632","namespace":"mcp_agent.mcp.mcp_aggregator.alert_manager_websocketorchestration","message":"Requesting tool call","data":{"data":{"progress_action":"Calling Tool","tool_name":"HttpNotification","server_name":"alert-notification","agent_name":"alert_manager_websocketorchestration"}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:18:55.518338","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: request=","data":{"data":{"method":"tools/call","params":{"meta":null,"name":"HttpNotification","arguments":{"alert_message":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies","subject":"[HIGH PRIORITY] WebSocketOrchestration - Supply_Chain Alert","content":"\u26a0\ufe0f HIGH PRIORITY - Supply Chain Risk Alert\n\nCompany: WebSocketOrchestration\nMetric: Shortage Index\nCurrent Value: 0.600\nThreshold: 0.550\nVariance: Within acceptable range\n\nContext: Supply chain disruption detected. Review supplier performance and inventory levels.\n\nACTION REQUIRED WITHIN 1 HOUR:\n\u2022 Review supplier performance and contracts\n\u2022 Assess supply chain alternatives\n\u2022 Implement risk mitigation measures\n\u2022 Update procurement strategies"}}}}}
{"level":"DEBUG","timestamp":"2025-09-17T09:18:55.533562","namespace":"mcp_agent.mcp.mcp_agent_client_session","message":"send_request: response=","data":{"data":{"meta":null,"content":[{"type":"text","text":"\u2705 HTTP notification sent successfully","annotations":null,"meta":null}],"structuredContent":null,"isError":false}}}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.534860","namespace":"orchestrator.workflow_patterns","message":"Alert step: Sent notification tool events"}
{"level":"ERROR","timestamp":"2025-09-17T09:18:55.534962","namespace":"orchestrator.context_manager","message":"Alert context validation failed: 1 validation error for AlertContextData\nnotification_results\n  Input should be a valid list [type=list_type, input_value={'successful': 1, 'failed...hannels_used': ['http']}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.534986","namespace":"orchestrator.workflow_patterns","message":"Step alert_processing completed successfully in 0.02s"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.535004","namespace":"orchestrator.workflow_patterns","message":"Workflow workflow_d2398041 completed in 18.95s"}
{"level":"DEBUG","timestamp":"2025-09-17T09:18:55.535408","namespace":"orchestrator.context_manager","message":"Saved context to workflow_contexts/workflow_d2398041.json"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.535435","namespace":"orchestrator.context_manager","message":"Cleaned up context for workflow workflow_d2398041"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.535453","namespace":"orchestrator.orchestration_runner","message":"Financial query execution completed: workflow_d2398041 (success: True, time: 18.95s)"}
{"level":"INFO","timestamp":"2025-09-17T09:18:55.537141","namespace":"mcp_agent.financial_websocket_demo_client_9c0d7821","message":"MCPApp cleanup","data":{"data":{"progress_action":"Finished","target":"financial_websocket_demo_client_9c0d7821","agent_name":"mcp_application_loop"}}}
