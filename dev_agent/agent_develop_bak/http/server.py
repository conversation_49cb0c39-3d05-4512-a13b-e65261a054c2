from flask import Flask, request

app = Flask(__name__)

@app.route("/alert", methods=["POST"])
def alert():
    """
    處理 HTTP POST 請求，並回傳成功訊息。
    """
    data = request.json  # 取得 JSON 格式的請求資料
    print(data)
    return "✅ HTTP alert received successfully", 200

def main():
    print("Starting HTTP server...")  # 印出訊息表示伺服器啟動
    app.run(host="0.0.0.0", port=5000)  # 啟動 Flask 伺服器，監聽所有 IP 位址的 5000 埠

if __name__ == "__main__":  # 如果此程式是主程式
    main()  # 呼叫 main() 函式啟動伺服器