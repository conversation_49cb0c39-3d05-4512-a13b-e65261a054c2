"""Tool for adding two numbers."""
import paho.mqtt.client as mqtt

from typing import Dict, Any, Union
from pydantic import Field, BaseModel

from notification.interfaces.notification import NotificationResponse, Notification

broker_address = "************"  # Or your broker's address
port = 1883
topic = "/notification"  # The topic to publish to

class MqttInput(BaseModel):
    """Input schema for the Notification tool."""

    subject: str = Field(description="The subject of the alert", examples="")
    content: str = Field(description="The content of the alert message", examples="")


class MqttOutput(BaseModel):
    """Output schema for the Notification tool."""
    message: str = Field(description="The message indicating the result of the operation", examples="Notification sent successfully.")
    error: Union[str, None] = Field(default=None, description="An error message if the operation failed.")


class MqttTool(Notification):
    """Tool that sends MQTT alerts."""

    name = "MqttNotification"
    description = "Sends an MQTT alert to a specified topic."
    input_model = MqttInput
    output_model = MqttOutput

    def get_schema(self) -> Dict[str, Any]:
        """Get the JSON schema for this tool."""
        return {
            "name": self.name,
            "description": self.description,
            "input": self.input_model.model_json_schema(),
            "output": self.output_model.model_json_schema(),
        }

    async def execute(self, input_data: MqttInput) -> NotificationResponse:
        """
        Publish a message to the MQTT broker.
        
        Args:
            input_data (dict) = {
                subject (str): The subject of the message.
                content (str): The content of the message.
            }
        Returns:
            NotificationResponse (str): A response indicating the result of the operation.
        """
        try:
            client = mqtt.Client()
            client.connect(broker_address, port)
            msg = {"subject": input_data.subject, "content": input_data.content}
            client.publish(topic, str(msg))  # Publish the message as a string
            client.disconnect()
            output = MqttOutput(message="✅ MQTT notification sent successfully", error=None)
            return NotificationResponse.from_model(output)
        except Exception as e:
            raise Exception(f"❌ Failed to send MQTT notification: {str(e)}")