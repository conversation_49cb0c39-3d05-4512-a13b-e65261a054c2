"""Tool for adding two numbers."""
import smtplib

from typing import Dict, Any, Union
from pydantic import Field, BaseModel
from email.message import EmailMessage

from notification.interfaces.notification import NotificationResponse, Notification

smtp_server = "smtp.gmail.com"
smtp_port = 587
sender_email = "<EMAIL>"
password = "axkp dktp efyb jlrs"  # 如果有啟用 MFA，需使用應用程式密碼

class EmailInput(BaseModel):
    """Input schema for the Email tool."""

    subject: str = Field(description="The subject of the email", examples="")
    content: str = Field(description="The content of the email message", examples="")
    recipient: str = Field(description="The email address of the recipient", examples="")

class EmailOutput(BaseModel):
    """Output schema for the Email tool."""
    message: str = Field(description="The message indicating the result of the operation", examples="Email sent successfully.")
    error: Union[str, None] = Field(default=None, description="An error message if the operation failed.")


class EmailTool(Notification):
    """Tool that sends email alerts."""

    name = "EmailNotification"
    description = "Sends an email alert to a specified recipient."
    input_model = EmailInput
    output_model = EmailOutput

    def get_schema(self) -> Dict[str, Any]:
        """Get the JSON schema for this tool."""
        return {
            "name": self.name,
            "description": self.description,
            "input": self.input_model.model_json_schema(),
            "output": self.output_model.model_json_schema(),
        }

    async def execute(self, input_data: EmailInput) -> NotificationResponse:
        """   
        Send an email notification.
        Args:
            input_data (dict) = {
                subject (str): The subject of the email.
                content (str): The content of the email message.
                recipient (str): The email address of the recipient.
            }
        Returns:
            NotificationResponse (str): A response indicating the result of the operation.
        """

        # 建立郵件內容
        msg = EmailMessage()
        msg["From"] = sender_email
        msg["To"] = input_data.recipient
        msg["Subject"] = input_data.subject
        msg.set_content(input_data.content)

        try:
            # 發送郵件
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.ehlo()  # 啟動 SMTP 連線
                server.starttls()  # 啟用 TLS 加密
                server.login(sender_email, password)
                server.send_message(msg)
            return "✅ 郵件寄送成功"
        except Exception as e:
            raise Exception(f"❌ 郵件寄送失敗: {str(e)}")
        