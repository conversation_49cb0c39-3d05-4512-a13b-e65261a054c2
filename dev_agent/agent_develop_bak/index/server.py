"""example-mcp-server MCP Server implementation with SSE transport."""
import sys
from typing import List
sys.path.append("..")  # Adjust the path to import from the parent directory

import uvicorn

from mcp.server import Server
from mcp.server.fastmcp import FastMCP
from mcp.server.sse import SseServerTransport

from starlette.applications import Starlette
from starlette.requests import Request
from starlette.routing import Mount, Route
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware

from index.services.index_service import ShortageIndexService
from index.interfaces.index import ShortageIndex
from index.tools.index import ShortageIndexTool
from index.tools.weighted_index import WeightedShortageIndexTool

def get_available_tools() -> List[ShortageIndex]:
    """Get list of all available tools."""
    return [
        ShortageIndexTool(),
        WeightedShortageIndexTool()
    ]


def create_starlette_app(mcp_server: Server) -> Starlette:
    """Create a Starlette application that can serve the provided mcp server with SSE."""
    sse = SseServerTransport("/messages/")

    async def handle_sse(request: Request) -> None:
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,  # noqa: SLF001
        ) as (read_stream, write_stream):
            await mcp_server.run(
                read_stream,
                write_stream,
                mcp_server.create_initialization_options(),
            )

    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
            allow_credentials=True,
        )
    ]

    return Starlette(
        routes=[
            Route("/sse", endpoint=handle_sse),
            Mount("/messages/", app=sse.handle_post_message),
        ],
        middleware=middleware,
    )


# Initialize FastMCP server with SSE
mcp = FastMCP("example-mcp-server")
tool_service = ShortageIndexService()

# Register all tools and their MCP handlers
tool_service.register_tools(get_available_tools())
tool_service.register_mcp_handlers(mcp)

# Get the MCP server
mcp_server = mcp._mcp_server  # noqa: WPS437

# Create the Starlette app
app = create_starlette_app(mcp_server)

# Export the app
__all__ = ["app"]


def main():
    """Entry point for the server."""
    import argparse

    parser = argparse.ArgumentParser(description="Run MCP SSE-based server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=6969, help="Port to listen on")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload for development")
    args = parser.parse_args()

    # Run the server with auto-reload if enabled
    uvicorn.run(
        "index.server:app",  # Use the app from server_sse.py directly
        host=args.host,
        port=args.port,
        reload=args.reload,
        reload_dirs=["index"],  # Watch this directory for changes
        timeout_graceful_shutdown=5,  # Add timeout
    )


if __name__ == "__main__":
    main()
