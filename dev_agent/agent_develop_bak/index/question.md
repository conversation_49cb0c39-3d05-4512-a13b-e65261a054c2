## Question
### shortage index: 
```
cpu available is 1, require is 2, and gpu available is 2, require is 6, motherboard available is 1, require is 3, fans available is 1, require is 6
```

```
There is 1 CPU available but 2 are needed; 2 GPUs are available while 6 are required. The system has 1 motherboard out of the 3 required, and only 1 fan is available compared to the 6 needed.
```


### weighted shortage index: 
```
cpu available is 1, require is 2, weight 0.2, and gpu available is 2, require is 6, weight 0.6, motherboard available is 1, require is 3, weight 0.1, fans available is 1, require is 6, weight 0.1
```

```
The system has 1 CPU available but needs 2 (weight: 0.2). There are 2 GPUs available out of the required 6 (weight: 0.6). For the motherboard, only 1 is available while 3 are required (weight: 0.1). As for the fans, 1 is available compared to the required 6 (weight: 0.1).
```

```
Available resources fall short of requirements: only 1 CPU is present out of 2 needed (importance: 0.2), 2 GPUs are available while 6 are required (importance: 0.6), the motherboard count stands at 1 versus a need for 3 (importance: 0.1), and just 1 fan is available compared to 6 required (importance: 0.1).
```