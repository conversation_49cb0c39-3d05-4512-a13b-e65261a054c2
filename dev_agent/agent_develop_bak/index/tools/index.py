"""Tool for adding two numbers."""

from typing import Dict, Any, Union
from pydantic import Field, BaseModel, ConfigDict

from index.interfaces.index import ShortageIndexResponse


class ShortageIndexInput(BaseModel):
    """Input schema for the ShortageIndex tool."""

    model_config = ConfigDict(
        json_schema_extra={"examples": [{"required_qty": [2, 4], "available_qty": [1, 2]}]}
    )

    required_qty: list[float] = Field(description="A list of required quantities", examples=[2, 4])
    available_qty: list[float] = Field(description="A list of available quantities", examples=[1, 2])


class ShortageIndexOutput(BaseModel):
    """Output schema for the ShortageIndex tool."""

    model_config = ConfigDict(json_schema_extra={"examples": [{"shortage_index": 0.7, "error": None}, {"shortage_index": 1.0, "error": None}]})

    shortage_index: float = Field(description="The calculated shortage index value.")
    error: Union[str, None] = Field(default=None, description="An error message if the operation failed.")


class ShortageIndexTool():
    """Tool that calculates the shortage index."""

    name = "ShortageIndex"
    description = "Calculates the shortage index based on required and available quantities and return shortage index."
    input_model = ShortageIndexInput
    output_model = ShortageIndexOutput

    def get_schema(self) -> Dict[str, Any]:
        """Get the JSON schema for this tool."""
        return {
            "name": self.name,
            "description": self.description,
            "input": self.input_model.model_json_schema(),
            "output": self.output_model.model_json_schema(),
        }

    async def execute(self, input_data: ShortageIndexInput) -> ShortageIndexResponse:
        """
        Calculate the shortage index based on the required and available quantities.
        The shortage index is defined as the (1.0 - minimum ratio of available to required quantities),
        which indicates how much of the required quantities are not available.  
        A value of 0.0 means that all required quantities are fully available,
        while a value of 1.0 means that none of the required quantities are available.

        The length of the required_qty and available_qty must be the same,
        and both lists must contain non-negative values.

        For example, if a GPU product has a required quantity of 2, an available quantity of 1, 
        and a CPU product has a required quantity of 4, an available quantity of 2, 
        then the arguments will be:
        required_qty = [2, 4]
        available_qty = [1, 2]
        The shortage index will be calculated as follows:
        shortage_index = 1 - min(available_qty[i] / required_qty[i] for i in range(len(required_qty)))
        
        Args:
            required_qty (list[float]): A list of required quantities.
            available_qty (list[float]): A list of available quantities.
        
        Returns:
            float: The calculated shortage index.
        """
        if len(input_data.required_qty) != len(input_data.available_qty):
            raise ValueError("Required and available quantities must have the same length.")
        l = []
        for r, a in zip(input_data.required_qty, input_data.available_qty):
            if r < 0 or a < 0:
                raise ValueError("Required quantity and available quantity must be non-negative.")            
            l.append(a / r if r > 0 else 1.0)        
        shortage_index = 1 - min(l)

        output = ShortageIndexOutput(shortage_index=shortage_index, error=None)
        return ShortageIndexResponse.from_model(output)
