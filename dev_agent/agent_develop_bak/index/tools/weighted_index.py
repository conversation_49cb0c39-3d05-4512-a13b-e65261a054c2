"""Tool for adding two numbers."""

from typing import Dict, Any, Union
from pydantic import Field, BaseModel, ConfigDict

from index.interfaces.index import ShortageIndexResponse


class WeightedShortageIndexInput(BaseModel):
    """Input schema for the ShortageIndex tool."""

    model_config = ConfigDict(
        json_schema_extra={"examples": [{"required_qty": [2, 4], "available_qty": [1, 2]}]}
    )

    required_qty: list[float] = Field(description="A list of required quantities", examples=[2, 4])
    available_qty: list[float] = Field(description="A list of available quantities", examples=[1, 2])
    weight: list[float] = Field(description="A list of weight factors corresponding to each quantity", examples=[0.6, 0.4])

class WeightedShortageIndexOutput(BaseModel):
    """Output schema for the ShortageIndex tool."""

    model_config = ConfigDict(json_schema_extra={"examples": [{"shortage_index": 0.7, "error": None}, {"shortage_index": 1.0, "error": None}]})

    weighted_shortage_index: float = Field(description="The calculated shortage index value.")
    error: Union[str, None] = Field(default=None, description="An error message if the operation failed.")


class WeightedShortageIndexTool():
    """Tool that calculates the shortage index."""

    name = "WeightedShortageIndex"
    description = "Calculates the weighted shortage index based on required, available quantities, weight factors, and return weighted shortage index."
    input_model = WeightedShortageIndexInput
    output_model = WeightedShortageIndexOutput

    def get_schema(self) -> Dict[str, Any]:
        """Get the JSON schema for this tool."""
        return {
            "name": self.name,
            "description": self.description,
            "input": self.input_model.model_json_schema(),
            "output": self.output_model.model_json_schema(),
        }

    async def execute(self, input_data: WeightedShortageIndexInput) -> ShortageIndexResponse:
        """
        Calculate the weighted shortage index based on the required, available quantities, and weight factors.
        The weighted shortage index is defined as the sum of the weighted shortages for each quantity,
        where the weight is applied to each shortage based on its relative importance.
        A value of 0.0 means that all required quantities are fully available,
        while a value of 1.0 means that none of the required quantities are available.

        The length of the required_qty, available_qty, and weight must be the same,
        and all lists must contain non-negative values. The weight must sum to 1.

        For example, if a GPU product has a required quantity of 2, an available quantity of 1, weight of 0.6,
        and a CPU product has a required quantity of 4, an available quantity of 2, weight of 0.4,
        then the arguments will be:
        required_qty = [2, 4]
        available_qty = [1, 2]
        weight = [0.6, 0.4]
        The weighted shortage index will be calculated as follows:
        weighted_shortage_index = sum(weight[i] * (required_qty[i] - available_qty[i]) / required_qty[i] for i in range(len(required_qty)))

        Args:
            required_qty (list[float]): A list of required quantities.
            available_qty (list[float]): A list of available quantities.
            weight (list[float]): A list of weight factors corresponding to each quantity.

        Returns:
            float: The calculated weighted shortage index.
        """
        if len(input_data.required_qty) != len(input_data.available_qty) != len(input_data.weight):
            raise ValueError("Required, available quantities and weights must have the same length.")
        
        if sum(input_data.weight) != 1.0:
            raise ValueError("Weights must sum to 1.")

        weighted_shortage_index = 0.0
        for r, a, w in zip(input_data.required_qty, input_data.available_qty, input_data.weight):
            if r < 0 or a < 0 or w < 0:
                raise ValueError("Required quantity, available quantity and weight must be non-negative.")

            weighted_shortage_index += ((r - a) / r) * w if r > 0 else 0.0 

        output = WeightedShortageIndexOutput(weighted_shortage_index=weighted_shortage_index, error=None)
        print("*" * 20)
        print(f"output: {output}")
        return ShortageIndexResponse.from_model(output)